{"name": "gauzy-desktop-timer", "productName": "Gauzy Desktop Timer", "version": "0.1.0", "description": "Gauzy Desktop Timer", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "main": "index.js", "workspaces": {"packages": ["../../../dist/packages/contracts", "../../../dist/packages/desktop-core", "../../../dist/packages/desktop-activity", "../../../dist/packages/desktop-lib", "../../../dist/packages/desktop-window", "../../../dist/packages/ui-config", "../../../dist/packages/constants"]}, "build": {"appId": "com.ever.gauzydesktoptimer", "artifactName": "${name}-${version}.${ext}", "productName": "Gauzy Desktop Timer", "copyright": "Copyright © 2019-Present. Ever Co. LTD", "afterSign": "tools/notarize.js", "dmg": {"sign": false}, "asar": true, "npmRebuild": true, "asarUnpack": ["node_modules/screenshot-desktop/lib/win32", "node_modules/@sentry/electron", "node_modules/better-sqlite3", "node_modules/@sentry/profiling-node/lib"], "directories": {"buildResources": "icons", "output": "../desktop-timer-packages"}, "publish": [{"provider": "github", "repo": "ever-gauzy-desktop-timer", "releaseType": "release"}, {"provider": "spaces", "name": "ever", "region": "sfo3", "path": "/ever-gauzy-desktop-timer", "acl": "public-read"}], "mac": {"category": "public.app-category.developer-tools", "icon": "icon.icns", "target": ["zip", "dmg"], "asarUnpack": "**/*.node", "artifactName": "${name}-${version}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "tools/build/entitlements.mas.plist", "entitlementsInherit": "tools/build/entitlements.mas.plist", "extendInfo": {"NSAppleEventsUsageDescription": ">- Please allow access to script browser applications to detect the current URL when triggering instant lookup.", "NSMicrophoneUsageDescription": ">- This app requires microphone access when recording sounds or detecting loudness.", "NSCameraUsageDescription": ">- This app requires camera access when using the video sensing blocks."}}, "win": {"publisherName": "Ever", "target": [{"target": "nsis", "arch": ["x64"]}], "icon": "icon.ico", "verifyUpdateCodeSignature": false}, "linux": {"icon": "linux", "target": ["AppImage", "deb", "tar.gz"], "executableName": "gauzy-desktop-timer", "artifactName": "${name}-${version}.${ext}", "synopsis": "Desktop", "category": "Development"}, "nsis": {"oneClick": false, "perMachine": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "icon.ico", "artifactName": "${name}-${version}.${ext}", "deleteAppDataOnUninstall": true, "menuCategory": true}, "extraResources": ["./data/**/*", "databaseDir", {"from": "assets", "to": "assets"}], "extraFiles": ["./**/desktop-lib/**/migrations/*"]}, "dependencies": {"@datorama/akita": "^8.0.1", "@datorama/akita-ngdevtools": "^7.0.0", "@electron/remote": "^2.0.8", "@gauzy/contracts": "file:../../../dist/packages/contracts", "@gauzy/desktop-core": "file:../../../dist/packages/desktop-core", "@gauzy/desktop-activity": "file:../../../dist/packages/desktop-activity", "@gauzy/desktop-lib": "file:../../../dist/packages/desktop-lib", "@gauzy/desktop-window": "file:../../../dist/packages/desktop-window", "@gauzy/ui-config": "file:../../../dist/packages/ui-config", "@gauzy/constants": "file:../../../dist/packages/constants", "@sentry/electron": "^6.8.0", "@sentry/node": "^9.43.0", "@sentry/profiling-node": "^9.43.0", "auto-launch": "5.0.5", "consolidate": "^0.16.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-util": "^0.18.1", "embedded-queue": "^0.0.11", "form-data": "^4.0.1", "htmlparser2": "^8.0.2", "knex": "^3.1.0", "libsql": "^0.3.16", "moment": "^2.30.1", "node-fetch": "^2.6.7", "node-notifier": "^8.0.0", "mysql2": "^3.12.0", "pg": "^8.13.1", "pg-query-stream": "^4.7.1", "screenshot-desktop": "^1.15.0", "sound-play": "1.1.0", "squirrelly": "^8.0.8", "tslib": "^2.6.2", "twing": "^5.0.2", "locutus": "^2.0.30", "underscore": "^1.13.3", "undici": "^6.10.2", "custom-electron-titlebar": "^4.2.8"}}