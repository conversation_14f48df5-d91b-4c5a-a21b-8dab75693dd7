{"name": "gauzy-desktop", "productName": "<PERSON>", "version": "0.1.0", "description": "<PERSON>", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "main": "index.js", "workspaces": {"packages": ["../../../dist/packages/auth", "../../../dist/packages/common", "../../../dist/packages/config", "../../../dist/packages/constants", "../../../dist/packages/contracts", "../../../dist/packages/core", "../../../dist/packages/desktop-core", "../../../dist/packages/desktop-activity", "../../../dist/packages/desktop-lib", "../../../dist/packages/desktop-window", "../../../dist/packages/plugin", "../../../dist/packages/plugins/changelog", "../../../dist/packages/plugins/integration-ai", "../../../dist/packages/plugins/integration-hubstaff", "../../../dist/packages/plugins/integration-github", "../../../dist/packages/plugins/integration-jira", "../../../dist/packages/plugins/integration-make-com", "../../../dist/packages/plugins/integration-upwork", "../../../dist/packages/plugins/integration-wakatime", "../../../dist/packages/plugins/integration-zapier", "../../../dist/packages/plugins/jitsu-analytics", "../../../dist/packages/plugins/job-proposal", "../../../dist/packages/plugins/job-search", "../../../dist/packages/plugins/knowledge-base", "../../../dist/packages/plugins/product-reviews", "../../../dist/packages/plugins/sentry-tracing", "../../../dist/packages/plugins/videos", "../../../dist/packages/plugins/registry", "../../../dist/packages/plugins/posthog", "../../../dist/packages/plugins/camshot", "../../../dist/packages/plugins/soundshot", "../../../dist/packages/ui-config", "../../../dist/packages/utils"]}, "build": {"appId": "com.ever.gauzydesktop", "artifactName": "${name}-${version}.${ext}", "productName": "<PERSON>", "copyright": "Copyright © 2019-Present. Ever Co. LTD", "afterSign": "tools/notarize.js", "dmg": {"sign": false}, "asar": true, "npmRebuild": true, "asarUnpack": ["node_modules/screenshot-desktop/lib/win32", "node_modules/@sentry/electron", "node_modules/better-sqlite3", "node_modules/@sentry/profiling-node/lib"], "directories": {"buildResources": "icons", "output": "../desktop-packages"}, "publish": [{"provider": "github", "repo": "ever-gauzy-desktop", "releaseType": "release"}, {"provider": "spaces", "name": "ever", "region": "sfo3", "path": "/ever-gauzy-desktop", "acl": "public-read"}], "mac": {"category": "public.app-category.developer-tools", "icon": "icon.icns", "target": ["zip", "dmg"], "asarUnpack": "**/*.node", "artifactName": "${name}-${version}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "tools/build/entitlements.mas.plist", "entitlementsInherit": "tools/build/entitlements.mas.plist", "extendInfo": {"NSAppleEventsUsageDescription": "Please allow access to script browser applications to detect the current URL when triggering instant lookup.", "NSMicrophoneUsageDescription": "This app requires microphone access when recording sounds or detecting loudness.", "NSCameraUsageDescription": "This app requires camera access when using the video sensing blocks."}}, "win": {"publisherName": "Ever", "target": [{"target": "nsis", "arch": ["x64"]}], "icon": "icon.ico", "verifyUpdateCodeSignature": false}, "linux": {"icon": "linux", "target": ["AppImage", "deb", "tar.gz"], "executableName": "gauzy-desktop", "artifactName": "${name}-${version}.${ext}", "synopsis": "Desktop", "category": "Development"}, "nsis": {"oneClick": false, "perMachine": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "icon.ico", "artifactName": "${name}-${version}.${ext}", "deleteAppDataOnUninstall": true, "menuCategory": true}, "extraResources": ["./data/**/*"]}, "dependencies": {"@datorama/akita-ngdevtools": "^7.0.0", "@datorama/akita": "^8.0.1", "@electron/remote": "^2.0.8", "@gauzy/auth": "file:../../../dist/packages/auth", "@gauzy/config": "file:../../../dist/packages/config", "@gauzy/constants": "file:../../../dist/packages/constants", "@gauzy/contracts": "file:../../../dist/packages/contracts", "@gauzy/core": "file:../../../dist/packages/core", "@gauzy/desktop-core": "file:../../../dist/packages/desktop-core", "@gauzy/desktop-activity": "file:../../../dist/packages/desktop-activity", "@gauzy/desktop-lib": "file:../../../dist/packages/desktop-lib", "@gauzy/desktop-window": "file:../../../dist/packages/desktop-window", "@gauzy/plugin": "file:../../../dist/packages/plugin", "@gauzy/plugin-changelog": "file:../../../dist/packages/plugins/changelog", "@gauzy/plugin-integration-ai": "file:../../../dist/packages/plugins/integration-ai", "@gauzy/plugin-integration-github": "file:../../../dist/packages/plugins/integration-github", "@gauzy/plugin-integration-hubstaff": "file:../../../dist/packages/plugins/integration-hubstaff", "@gauzy/plugin-integration-jira": "file:../../../dist/packages/plugins/integration-jira", "@gauzy/plugin-integration-make-com": "file:../../../dist/packages/plugins/integration-make-com", "@gauzy/plugin-integration-upwork": "file:../../../dist/packages/plugins/integration-upwork", "@gauzy/plugin-integration-wakatime": "file:../../../dist/packages/plugins/integration-wakatime", "@gauzy/plugin-integration-zapier": "file:../../../dist/packages/plugins/integration-zapier", "@gauzy/plugin-jitsu-analytics": "file:../../../dist/packages/plugins/jitsu-analytics", "@gauzy/plugin-job-proposal": "file:../../../dist/packages/plugins/job-proposal", "@gauzy/plugin-job-search": "file:../../../dist/packages/plugins/job-search", "@gauzy/plugin-knowledge-base": "file:../../../dist/packages/plugins/knowledge-base", "@gauzy/plugin-product-reviews": "file:../../../dist/packages/plugins/product-reviews", "@gauzy/plugin-sentry": "file:../../../dist/packages/plugins/sentry-tracing", "@gauzy/plugin-videos": "file:../../../dist/packages/plugins/videos", "@gauzy/plugin-registry": "file:../../../dist/packages/plugins/registry", "@gauzy/plugin-posthog": "file:../../../dist/packages/plugins/posthog", "@gauzy/plugin-camshot": "file:../../../dist/packages/plugins/camshot", "@gauzy/plugin-soundshot": "file:../../../dist/packages/plugins/soundshot", "@gauzy/ui-config": "file:../../../dist/packages/ui-config", "@gauzy/utils": "file:../../../dist/packages/utils", "@nestjs/platform-express": "^11.1.0", "@sentry/electron": "^6.8.0", "@sentry/node": "^9.43.0", "@sentry/profiling-node": "^9.43.0", "auto-launch": "5.0.5", "consolidate": "^0.16.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-util": "^0.18.1", "embedded-queue": "^0.0.11", "form-data": "^4.0.1", "htmlparser2": "^8.0.2", "knex": "^3.1.0", "libsql": "^0.3.16", "locutus": "^2.0.30", "moment": "^2.30.1", "node-fetch": "^2.6.7", "node-notifier": "^8.0.0", "node-static": "^0.7.11", "pdfmake": "^0.2.0", "pg-query-stream": "^4.7.1", "pg": "^8.13.1", "screenshot-desktop": "^1.15.0", "sound-play": "1.1.0", "squirrelly": "^8.0.8", "tslib": "^2.6.2", "twing": "^5.0.2", "underscore": "^1.13.3", "undici": "^6.10.2", "custom-electron-titlebar": "^4.2.8"}, "optionalDependencies": {"node-linux": "^0.1.12", "node-mac": "^1.0.1", "node-windows": "^1.0.0-beta.8"}}