{"name": "@gauzy/ui-core", "version": "0.1.0", "description": "A collection of core UI components and utilities for the Gauzy framework. This library provides essential building blocks for constructing user interfaces, designed to be flexible and reusable in various Angular applications.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/ui-core"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "scripts": {"lib:build": "yarn nx build ui-core --configuration=development", "lib:build:prod": "yarn nx build ui-core --configuration=production", "lib:watch": "yarn nx build ui-core --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"@ali-hm/angular-tree-component": "^19.2.5", "@angular-slider/ngx-slider": "^19.0.0", "@angular/animations": "^19.2.10", "@angular/cdk": "^19.2.11", "@angular/forms": "^19.2.10", "@angular/material": "^19.2.11", "@angular/platform-browser": "^19.2.10", "@angular/router": "^19.2.10", "@bluehalo/ngx-leaflet": "^19.0.0", "@datorama/akita": "^8.0.1", "@electron/remote": "^2.0.8", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/bootstrap": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/moment-timezone": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@gauzy/constants": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@jitsu/js": "^1.10.0", "@kurkle/color": "^0.3.4", "@nebular/auth": "^15.0.0", "@nebular/bootstrap": "^9.1.0-rc.6", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@ng-maps/core": "^8.0.0", "@ng-maps/google": "^8.0.0", "@ng-select/ng-select": "^14.8.1", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@sentry/angular": "^9.43.0", "angular2-smart-table": "^3.6.2", "bootstrap": "^4.3.1", "camelcase": "^6.3.0", "chart.js": "^4.4.9", "chartjs-plugin-annotation": "^3.0.1", "ckeditor4": "4.22.1", "ckeditor4-angular": "4.0.1", "date-fns": "^2.28.0", "date-holidays": "^1.6.1", "echarts": "^5.6.0", "eva-icons": "^1.1.3", "file-saver": "^2.0.5", "fullcalendar": "^6.1.15", "hotkeys-js": "^3.12.0", "leaflet": "^1.8.0", "moment": "^2.30.1", "moment-range": "^4.0.2", "moment-timezone": "^0.5.48", "nebular-icons": "^1.1.0", "ng2-charts": "^8.0.0", "ng2-file-upload": "^8.0.0", "ngx-clipboard": "^16.0.0", "ngx-color-picker": "^19.0.0", "ngx-cookie-service": "^19.1.2", "ngx-countdown": "^19.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-draggable-dom": "^19.0.7", "ngx-infinite-scroll": "^19.0.0", "ngx-moment": "^6.0.2", "ngx-permissions": "^19.0.0", "randomcolor": "^0.6.2", "rxjs": "^7.8.2", "slugify": "^1.6.6", "swiper": "^8.3.1", "tslib": "^2.6.2", "underscore": "^1.13.3", "underscore.string": "^3.3.6", "uuid": "^11.1.0"}, "devDependencies": {"@types/ckeditor": "^4.9.10", "@types/jest": "29.5.14", "@types/node": "^20.14.9", "jest-preset-angular": "14.5.5"}, "keywords": ["ui-core", "angular", "core-components", "utilities", "ui-framework", "ever-gauzy", "nebular", "angular-material", "rxjs", "ngx", "typescript", "leaflet", "moment", "ngx-translate", "charts", "open-source", "enterprise", "agpl"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}