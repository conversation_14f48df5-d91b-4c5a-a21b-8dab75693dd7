{"BUTTONS": {"PAY": "Pay", "ADD_EXISTING_USER": "Add Existing User", "ADD_NEW": "Add New", "ADD": "Add", "ADD_INTEGRATION": "Add Integration", "RESET_INTEGRATION": "Reset Integration", "CREATE": "Create", "REGISTER": "Register", "CHANGE_PASSWORD": "Change password", "LOGIN": "Log In", "SIGNIN": "Sign in", "SEND_CODE": "Send code", "SENDING_CODE": "Sending code...", "ADD_NOTE": "Add note", "EDIT": "Edit", "MANAGE": "Manage", "DETAILS": "Details", "DUPLICATE": "Duplicate", "DELETE": "Delete", "REMOVE": "Remove", "ADD_EXISTING": "Add Existing", "CONVERT_TO_EMPLOYEE": "Convert to employee", "OK": "Ok", "YES": "Yes", "NO": "No", "SAVE": "Save", "CLEAR_ALL": "Clear All", "BACK": "BACK", "SENT": "<PERSON><PERSON>", "ACCEPTED": "Accepted", "MARK_AS_SENT": "<PERSON> as <PERSON><PERSON>", "MARK_AS_ACCEPTED": "<PERSON> as Accepted", "CANCEL": "Cancel", "CLOSE": "Close", "INVITE": "Invite", "SELECT_ALL": "Select All", "COPY_LINK": "Copy Link", "COPIED": "Copied!", "MANAGE_INTERVIEWS": "Manage Interviews", "MANAGE_INVITES": "Manage Invites", "MANAGE_SPRINTS": "Manage Sprints", "RESEND": "Resend", "NEXT": "Next", "PREVIOUS": "Previous", "INVITE_AGAIN": "Invite Again", "REQUEST": "Request", "HISTORY": "History", "SYNC": "Sync", "SYNCING": "Syncing...", "SELECTED_TASKS": "Selected Tasks", "UPDATE": "Update", "AUTO_SYNC": "Auto sync", "AUTO_SYNCING": "Auto syncing...", "VIEW": "View", "SEND": "Send", "ARCHIVE": "Archive", "HIRE": "<PERSON>re", "MANAGE_CATEGORIES": "Manage Categories", "REJECT": "Reject", "FIND_TIME": "Find time", "DOWNLOAD": "Download", "ADD_KNOWLEDGE_BASE": "Add Knowledge Base", "CHOOSE_ICON": "Choose icon", "MAKE_PRIVATE": "make private", "MAKE_PUBLIC": "make public", "KNOWLEDGE_BASES": "Knowledge bases", "SELECT": "Select", "EMAIL": "Email", "CONVERT_TO_INVOICE": "Convert to invoice", "TO_INVOICE": "To invoice", "PUBLIC_APPOINTMENT_BOOK": "Book Public Appointment", "SAVE_AS_DRAFT": "Save as Draft", "SAVE_AND_SEND_CONTACT": "Save and send to contact in gauzy", "SAVE_AND_SEND_EMAIL": "Save and send via email", "EVENT_TYPES": "Event Types", "SEARCH": "Search", "RESET": "Reset", "LEAVE_FEEDBACK": "Leave a feedback", "SPRINT": {"CREATE": "Create Sprint", "EDIT": "Edit", "DELETE": "Delete"}, "CANDIDATE_STATISTIC": "Statistic", "FILTER": "Filter", "REFRESH": "Refresh", "AUTO_REFRESH": "Auto Refresh", "PROPOSAL_DELETE_MESSAGE": "Proposal template successfully delete", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "Proposal template successfully make as default", "MANAGE_TEMPLATES": "Manage Templates", "MAKE_DEFAULT": "Make Default", "REMOVE_DEFAULT": "<PERSON><PERSON><PERSON>", "HIDE_ALL": "Hide All", "SCHEDULES": "Schedules", "YES_HIDE_ALL_JOBS": "Yes, Hide All Jobs", "VALIDATE": "Validate", "VALIDATED": "Validated", "APPROVE": "Approve", "DENY": "<PERSON><PERSON>", "PAYMENTS": "Payments", "NOTE": "Note", "SKIP_CONTINUE": "Skip {{ label }} and Continue", "BUY": "Buy", "DELETE_ACCOUNT": "Delete your account", "DELETE_ALL_DATA": "Delete all data", "SELECT_AND_CONTINUE": "Select and Continue", "ADD_KPI": "Add KPI", "PAID_DAYS_OFF": "Paid days off", "UNPAID_DAYS_OFF": "Unpaid days off", "CLEAR": "Clear", "SET": "Set", "RECORD_FULL_PAYMENT": "Record Full Payment", "EXPORT_TO_CSV": "Export to CSV", "INVOICE_REMAINING_AMOUNT": "Invoice Remaining Amount", "PUBLIC_LINK": "Public Link", "GENERATE": "Generate", "SEND_RECEIPT": "Send Receipt", "ADD_COMMENT": "Add Comment", "CONTINUE": "Continue", "SUPER_ADMIN_DEMO": "Super Admin Demo", "ADMIN_DEMO": "<PERSON><PERSON>", "EMPLOYEE_DEMO": "Employee Demo", "DEMO_CREDENTIALS": "Demo Credentials", "CREATE_NEW_ROLE": "Create {{ name }} Role", "DELETE_EXISTING_ROLE": "Delete {{ name }} Role", "RESTORE": "Rest<PERSON>", "VIEW_ALL": "View All", "VIEW_REPORT": "View Report", "TIME_TRACKING_ENABLE": "Enable Time Tracking", "TIME_TRACKING_DISABLE": "Disable Time Tracking", "PRINT": "Print", "FEEDBACK": "<PERSON><PERSON><PERSON>", "EQUIPMENT_SHARING": "Equipment Sharing", "PRIVATE": "Private", "PUBLIC": "Public", "MANAGE_WIDGET": "Manage widgets", "MOVE": "Move", "COLLAPSE": "Collapse", "EXPAND": "Expand", "SHOW_MORE": "Show More", "START_WORK": "Start Work", "APPLY": "Apply", "GENERATE_PROPOSAL": "Generate Proposal", "LET_S_GO": "Let's go", "CHECK": "Check", "CHECK_UPDATE": "Check Update", "DOWNLOAD_NOW": "Download Now", "UPDATE_NOW": "Update Now", "SAVE_RESTART": "Save & Restart", "FILES": "Files", "START": "Start", "STOP": "Stop", "ACKNOWLEDGE": "Acknowledge", "RESTART": "<PERSON><PERSON>", "LATER": "Later", "UPGRADE": "Upgrade", "SKIP_NOW": "Skip Now", "EXIT": "Exit", "LOGOUT": "Logout", "COPY": "Copy", "CUT": "Cut", "PASTE": "Paste", "FORWARD_PORTS": "Forward Ports", "REPORT": "Report", "IGNORE": "Ignore", "ENABLED": "Enabled", "DISABLED": "Disabled", "RESYNC": "Resync", "INSTALL": "Install", "UNINSTALL": "Uninstall", "DEACTIVATE": "Deactivate", "ACTIVATE": "Activate", "ADD_PLUGIN": "<PERSON><PERSON> P<PERSON>in", "LOAD_PLUGIN": "<PERSON><PERSON>", "FROM_CDN": "From CDN", "ADD_MODULE": "<PERSON><PERSON>", "SHARE": "Share", "GET_INFO": "Get Info", "RETRY": "Retry", "UPLOAD": "Upload", "ADD_VERSION": "Add Version", "MARKETPLACE": "Marketplace", "READ_MORE": "Read More", "READ_LESS": "Read Less", "RECOVER": "Recover", "ADD_MORE_SOURCES": "Add More Sources", "REMOVE_FROM_FAVORITES": "Remove from favorites", "ADD_TO_FAVORITES": "Add to favorites"}, "SM_TABLE": {"NO_DATA": {"LOADING": "Loading, Please wait...", "RECEIVE_ESTIMATE": "You have not received any estimates.", "INCOME": "You have not created any incomes.", "EXPENSE_CATEGORY": "You have not created any expense categories.", "REPORT": "You have not created any reports.", "CONTRACT": "You have not created any contracts.", "TEAM": "You have not created any teams.", "HISTORY_RECORD": "You have not created any records.", "PROFIT_HISTORY": "You have not created any profit histories.", "EMPLOYEE": "You have not created any employees.", "EXPENSE": "You have not created any expenses.", "PAYMENT": "You have not received any payments.", "PROPOSAL_TEMPLATE": "You have not created any proposal templates.", "PROPOSAL": "You have not created any proposals.", "PIPELINE": "You have not created any pipelines.", "TASK": "You have not created any tasks.", "INVITE": "You have not invited any users.", "APPROVAL_REQUEST": "You have not created any approval requests.", "APPROVAL_POLICY": "You have not created any approval policies.", "TIME_OFF": "You have not created any time offs.", "TIME_OFF_POLICY": "You have not created any time off policies.", "CANDIDATE": "You have not created any candidates.", "INTERVIEW": "You have not created any interviews.", "EQUIPMENT": "You have not created any equipment.", "EQUIPMENT_SHARING": "You have not created any equipment sharing records.", "EQUIPMENT_SHARING_POLICY": "You have not created any equipment sharing policies.", "INVENTORY": "You have not created any inventories.", "MERCHANT": "You have not created any merchants.", "WAREHOUSE": "You have not created any warehouses.", "WAREHOUSE_PRODUCT": "You have not created any warehouse products.", "PRODUCT_CATEGORY": "You have not created any product categories.", "TAGS": "You have not created any tags.", "PROJECT": "You have not created any projects.", "DEPARTMENT": "You have not created any departments.", "CONTACT": "You have not created any contacts.", "CLIENT": "You have not created any clients.", "LEAD": "You have not created any leads.", "TIME_FRAME": "You have not created any time frames.", "KPI": "You have not created any KPIs.", "INVOICE": "You have not created any invoices.", "ESTIMATE": "You have not created any estimates.", "EVENT_TYPE": "You have not created any event types.", "PRODUCT_TYPE_NO_DATA": "You have not created any product types.", "TEAM_DASHBOARD": "You don't have any teams.", "PLUGIN": "You haven't install any plugin.", "PLUGIN_MARKETPLACE": "This plugin is unvailable on marketplace", "HISTORY": "There's no history"}, "TRANSACTION_TYPE": "Type", "AMOUNT": "Amount", "DATE": "Date", "TITLE": "Title", "STAGE": "Stage", "START_DATE": "Start Date", "END_DATE": "End Date", "CLIENT_NAME": "Client Name", "CONTACT_NAME": "Contact Name", "NAME": "Name", "VENDOR": "<PERSON><PERSON><PERSON>", "CATEGORY": "Category", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "VALUE": "Value", "NOTES": "Notes", "EMPLOYEE": "Employee", "EMPLOYEES": "Employees", "FULL_NAME": "Full Name", "EMAIL": "Email", "INCOME": "Income (Average)", "EXPENSES": "Expenses (Average)", "BONUS": "Bonus", "BONUS_AVG": "Bonus (Average)", "PROFIT_BASED_BONUS": "Profit Based Bonus", "REVENUE_BASED_BONUS": "Revenue Based Bonus", "STATUS": "Status", "SOURCE": "Source", "TODAY": "Today", "END_OF_MONTH": "End Of Month", "START_OF_MONTH": "Start Of Month", "RATE": "Rate", "FLAT_FEE": "Flat Fee", "MILESTONES": "Milestones", "JOB_TITLE": "Job Title", "JOB_POST_URL": "Job Post URL", "LINK_TO_JOBPOST": "Link to Job Post", "AUTHOR": "Author", "MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "SUNDAY": "Sunday", "NONE": "None", "ROLE": "Role", "PROJECTS": "Projects", "PROJECT": "Project", "INVITED_BY": "Invited By", "EXPIRE_DATE": "Expires", "CLIENTS": "Clients", "CONTACTS": "Contacts", "CONTACT": "Contact", "DEPARTMENTS": "Departments", "DESCRIPTION": "Description", "POLICY": "Policy", "APPLIED": "Applied", "HIRED": "<PERSON><PERSON>", "REJECTED": "Rejected", "NO_RESULT": "No Result", "CLIENT": "Client", "INTERNAL": "Internal", "START": "Start", "END": "End", "REQUEST_DATE": "Request Date", "REGION": {"BG": "Български (България)", "EN": "English (United States)", "RU": "Русский (Россия)", "HE": "עברית ישראל", "FR": "<PERSON><PERSON><PERSON> (France)", "ES": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)", "ZH": "中文 (中国)", "DE": "Deutsch (Deutschland)", "PT": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)", "IT": "Italiano (Italia)", "NL": "Nederlands (Nederland)", "PL": "Polski (Polska)", "AR": "العربية (العراق)"}, "CURRENT_VALUE": "Current value", "TARGET_VALUE": "Target value", "LAST_UPDATED": "Last Updated", "LAST_SYNC_DATE": "Last Sync Date", "CREATED_BY": "Created By", "NO_DATA_MESSAGE": "No Data", "TAGS": "Tags", "LABELS": "Labels", "CREATED": "Created", "APPLIED_DATE": "Applied Date", "HIRED_DATE": "<PERSON><PERSON>", "REJECTED_DATE": "Rejected Date", "TIME_TRACKING": "Time Tracking", "CREATED_AT": "Created At", "SCREEN_CAPTURE": "Screen Capture", "NUMBER": "Number", "PROVIDER": "Provider", "GITHUB_REPOSITORY": "GitHub Repository", "ISSUES_SYNC": "Issues in Sync", "ISSUES_SYNC_COUNT": "{{ count }} Issues Auto Sync", "RESYNC_ISSUES": "Resync Issues", "ENABLED_DISABLED_SYNC": "Enabled / Disabled Sync", "ENABLE_DISABLE_INTEGRATION": "Enable / Disable Integration", "ACTIONS": "Actions"}, "FORM": {"USERNAME": "Username", "PASSWORD": "Password", "CONFIRM": "Confirm", "FILTER": "Filter", "EMAIL": "Email", "LABELS": {"NAME": "Name", "PHONE_NUMBER": "Phone Number", "WEBSITE": "Website", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name (optional)", "FROM": "From", "TO": "To", "EMPLOYEE": "Employee", "START_DATE": "Date when started work (optional)", "APPLIED_DATE_LABEL": "Date when applied", "IMAGE_URL": "Image URL (optional)", "CV_URL": "CV URL (optional)", "DOCUMENT_URL": "Document URL", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "DATE_TYPE": "Default Date Type", "ADD_TEAM": "Add New Team", "EDIT_TEAM": "Edit Team", "OFFICIAL_NAME": "Official Name", "PROFILE_LINK": "Profile Link", "START_WEEK_ON": "Start Week On", "TAX_ID": "Tax ID", "TIME_FORMAT": "Time Format", "COUNTRY": "Country", "CITY": "City", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "LOGO_ALIGNMENT": "Logo Alignment", "BRAND_COLOR": "Brand Color", "DATE_FORMAT": "Date Format", "CHOOSE_TIME_ZONE": "Choose Time Zone", "START_TIME": "Default Work Day Start Time", "END_TIME": "Default Work Day End Time", "POSTCODE": "Postcode (Zip)", "PAY_PERIOD": "Pay Period", "BILL_RATE": "Billing Rate (per hour)", "BILL_RATE_MIN": "Minimum Billing Rate (per hour)", "CURRENCY_PER_HOUR": "<PERSON><PERSON><PERSON><PERSON>", "RECURRING_WEEKLY_LIMIT": "Recurring Weekly Limit (hours)", "ROLE": "Role", "SOURCE": "Source (optional)", "EMAILS": "Email Addresses", "PROJECTS_OPTIONAL": "Projects (Optional)", "CONTACTS_OPTIONAL": "Contacts (Optional)", "DEPARTMENTS_OPTIONAL": "Departments (Optional)", "TEAMS_OPTIONAL": "Teams (Optional)", "PROJECTS": "Projects", "ADD_NEW_DEPARTMENT": "Add New Department", "EDIT_DEPARTMENT": "Edit Department", "TYPE_OF_BONUS": "Employee Bonus Type", "BONUS_PERCENTAGE": "Bonus Percentage", "ENABLE_DISABLE_INVITES": "Enable/Disable Invites", "ALLOW_USER_INVITES": "Allow Users to send invites", "INVITE_EXPIRY_PERIOD": "Invite Expiry Period (in Days)", "EMPLOYMENT_TYPES": "Employment Type", "ADD_NEW_EMPLOYMENT_TYPE": "Add New Employment Type", "OFFER_DATE": "Offer Date (optional)", "ACCEPT_DATE": "Accept Date (optional)", "APPLIED_DATE": "Applied Date (optional)", "EDUCATION": "Education (optional)", "EXPERIENCE": "Work experience (optional)", "SKILLS": "Skills (optional)", "HIRED_DATE": "Hired Date (optional)", "REJECT_DATE": "Reject Date (optional)", "DOCUMENT_NAME": "Document name", "FEEDBACK_DESCRIPTION": "Feedback description", "EMAIL_INVITATION": "Enter email to send invitation", "SELECT_EQUIPMENT": "Select equipment", "SELECT_SHARE_REQUEST_DATE": "Select request date", "SELECT_SHARE_START_DATE": "Select start date", "SELECT_SHARE_END_DATE": "Select end date", "SELECT_EMPLOYEE": "Select Employee", "SELECT_TEAM": "Select Team", "ENABLE_DISABLE_FUTURE_DATE": "Enable/Disable future dates", "ALLOW_FUTURE_DATE": "Allow switching to future periods", "REGISTRATION_DATE": "Registration Date", "ORGANIZATION_NAME": "Organization Name", "MEETING_AGENDA": "Agenda", "MEETING_LOCATION": "Location", "MEETING_DESCRIPTION": "Description", "MEETING_INVITEES": "Invitees", "TITLE": "Title", "DATE": "Date", "TIME": "Time", "DURATION": "Duration", "CANDIDATE": "Candidate", "INTERVIEWERS": "Interviewers", "LOCATION": "Location", "NOTE": "Note", "PREFERRED_LANGUAGE": "Preferred Language", "DESCRIPTION": "Description", "DESCRIPTION_OPTIONAL": "Description (optional)", "ADD_OR_REMOVE_EMPLOYEES": "Add or remove employees", "ADD_REMOVE_MANAGERS": "Add or Remove Managers", "ADD_REMOVE_MEMBERS": "Add or Remove Members", "SHORT_DESCRIPTION": "Short Description", "ENABLE_EMPLOYEE_FEATURES": "Enable Employee Features", "REVOKE_EMPLOYEE_FEATURES": "Revoke Employee Features", "STATUS": "Status", "FISCAL_YEAR_START_DATE": "Fiscal Year Start Date", "FISCAL_YEAR_END_DATE": "Fiscal Year End Date", "TAX_AND_DISCOUNT_INVOICE_ITEMS_SEPARATELY": "Tax And Discount Invoice Items Separately", "ALLOW_TAXING_AND_DISCOUNTING_OF_INVOICE_ITEMS_SEPARATELY": "Allow taxing and discounting of invoice items separately", "DISCOUNT_AFTER_TAX": "Discount after tax", "APPLY_DISCOUNT_AFTER_TAX_FOR_INVOICES_AND_ESTIMATES": "Apply discount after tax for invoices and estimates", "FIND_ADDRESS": "Find Address", "LINKEDIN": "LinkedIn", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "TWITTER": "Twitter", "GITHUB": "GitHub", "GITLAB": "Gitlab", "UPWORK": "Upwork", "STACK_OVERFLOW": "Stackoverflow", "PROJECT_URL": "Project URL", "CLIENTS": "Clients", "IS_PROJECT_OPEN_SOURCE": "Is Project Open-Source", "OPEN_SOURCE_PROJECT_URL": "Open-Source Project URL", "EMPLOYEE_LEVEL": "Employee Level", "UNIT": "Unit", "SELECT_EXISTING_OBJECTIVE": "Select Existing Objective", "LENGTH": "Length", "DATE_START": "Start date", "END_DATE": "End Date", "GOAL": "Goal", "DOWNLOAD_REQUEST_FORM": "Download Request Form", "COORDINATE": {"TITLE": "COORDINATES", "LATITUDE": "Latitude", "LONGITUDE": "Longitude"}, "PUBLIC_LINK": "Public Link", "DEFAULT_TERMS": "Default terms for invoices and estimates", "CONVERT_ESTIMATES": "Convert Estimates", "ALLOW_CONVERTING": "Automatically convert accepted estimate to invoice", "DEFAULT_DAYS": "Default days until invoices and estimates are due.", "TEMPLATE_NAME": "Template Name", "TEMPLATE_BODY": "Template Body", "TEMPLATE_PREVIEW": "Template Preview", "LANGUAGE": "Language", "DEFAULT_INVOICE_TEMPLATE": "Default Invoice Template", "DEFAULT_ESTIMATE_TEMPLATE": "Default Estimate Template", "DEFAULT_RECEIPT_TEMPLATE": "Default Receipt Template", "DEFAULT": "Default Organization", "INVITATION_EXPIRATION": "Invitation Expiration", "PERIOD": "Period", "REGISTER_AS_EMPLOYEE_OF_ORGANIZATION": "Do you want to register as Employee of Organization?", "COVER_LETTER": "Cover Letter", "DETAILS": "Details", "HOURLY_RATE": "Hourly Rate", "ATTACHMENTS": "Attachments", "UPWORK_ORGANIZATION_ID": "Upwork Organization ID", "UPWORK_ORGANIZATION_NAME": "Upwork Organization Name", "UPWORK_ID": "Upwork ID", "LINKEDIN_ID": "LinkedIn ID", "AUTO_SYNC_TASKS": "Auto-sync tasks", "AUTO_SYNC_TASKS_BASED_ON_LABEL": "Is tasks Auto-sync based on Label?", "AUTO_SYNC_TAG": "Label", "GITHUB_REPOSITORY": "GitHub Repository", "PROJECT": "Project", "ENABLE_JOBS_SEARCH_MATCHING_ANALYSIS": "Do you want to enable Jobs Search & Matching Analysis?", "ENABLE_EMPLOYEE_PERFORMANCE_ANALYSIS": "Do you want to enable Employee Performance Analysis?", "STANDARD_WORK_HOURS_PER_DAY": "Standard Work Hours Per Day"}, "PLACEHOLDERS": {"NAME": "Name", "PHONE_NUMBER": "Phone Number", "DEFAULT": "Select Default", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "COMPANY_NAME": "Company name", "ALL_EMPLOYEES": "All Employees", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "ALL_CURRENCIES": "All Currencies", "ALL_DEPARTMENTS": "All Departments", "ALL_POSITIONS": "All Positions", "START_DATE": "Date", "PICK_DATE": "Pick a Date", "DATE_TYPE": "All Date Types", "BILLING": "Billing", "BILLABLE": "Billable", "CODE": "Code", "COLOR": "Color", "WEBSITE": "Website", "CURRENCY_POSITION": "Currency Position", "FISCAL_INFORMATION": "Fiscal Information", "IMAGE_URL": "Image", "ADD_DEPARTMENT": "Add Department", "ADD_POSITION": "Add position", "ADD_VENDOR": "Add vendor", "ADD_SKILL": "Add skill", "ADD_CANDIDATE_QUALITY": "Add personal quality", "ADD_TECHNOLOGY": "Add technology", "ADD_EXPENSE_CATEGORY": "Add expense category", "CONTACTS": "Contacts", "START_DATE_PROJECT": "Project Start Date", "END_DATE_PROJECT": "Project End Date", "TEAM_NAME": "Team Name", "ADD_REMOVE_MEMBERS": "Add or Remove Team Members", "ADD_REMOVE_MANAGERS": "Add or Remove Team Managers", "MEMBERS_COUNT": "Members count", "OFFICIAL_NAME": "Enter Official Name", "PROFILE_LINK": "Enter Profile Link", "START_WEEK_ON": "Start Week On", "TAX_ID": "Tax ID", "COUNTRY": "Country", "CITY": "City", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "EDUCATION": "Education", "EXPERIENCE": "Work experience", "SKILLS": "Skills", "POSTCODE": "Postcode (Zip)", "BILL_RATE": "Billing Rate (per hour)", "BILL_RATE_MIN": "Minimum Billing Rate (per hour)", "RECURRING_WEEKLY_LIMIT": "Recurring Weekly Limit (hours)", "EMAILS": "Type an email address and press enter", "ROLE": "Select Role", "PROJECTS": "Choose Projects", "CHOOSE_FORMAT": "Choose Format", "CHOOSE_TIME_ZONE": "Choose Time Zone", "TIME_FORMAT": "Time Format", "START_TIME": "HH:mm", "END_TIME": "HH:mm", "ADD_COLOR": "Add Color", "ALIGN_LOGO_TO": "Align Logo To", "DEPARTMENTS": "Departments", "TEAMS": "Teams", "NUMBER_FORMAT": "Select Number Format", "REGIONS": "Select Region", "REMOVE_IMAGE": "Remove image", "UPLOADER_PLACEHOLDER": "Image", "UPLOADER_DOCUMENT_PLACEHOLDER": "URL", "ADD_REMOVE_EMPLOYEES": "Add or Remove Employees", "ADD_REMOVE_PROJECTS": "Add or Remove Projects", "ADD_REMOVE_TEAMS": "Add or Remove Teams", "ADD_REMOVE_CANDIDATE": "Add Candidate", "ADD_REMOVE_EMPLOYEE": "Add Interviewer", "ADD_REMOVE_INTERVIEWER": "Select interviewer", "ADD_REMOVE_CANDIDATES": "Add or Remove Candidates", "ADD_REMOVE_USERS": "Add or Remove Users", "ADD_REMOVE_ORGANIZATIONS": "Add or Remove Organizations", "ADD_ORGANIZATIONS": "Add Organizations", "DATE": "Date", "VALUE": "Value", "SELECT_CURRENCY": "Select Currency", "TYPE_OF_BONUS": "Select Type of Bonus", "BONUS_PERCENTAGE": "Bonus Percentage", "ENABLE_INVITES": "Enable Invites", "INVITE_EXPIRY_PERIOD": "Invites valid upto", "SWITCH_PROJECT_STATE": "Public", "CHOOSE_EMPLOYEES": "Choose employee/s", "CHOOSE_TEAMS": "Choose team/s", "CHOOSE_APPROVAL_POLICY": "<PERSON>ose Approval Policy", "EMPLOYMENT_TYPES": "Employment Type", "REGISTRATION_DATE": "Organization Registration Date", "ORGANIZATIONS": "Choose Organizations", "ORGANIZATION": "Select Organization", "DOCUMENT_NAME": "Document name", "FEEDBACK_DESCRIPTION": "Feedback description", "PREFERRED_LANGUAGE": "Preferred Language", "OWNER": "Owner", "TASK_VIEW_MODE": "Task view mode", "SELECT_STATUS": "Select Status", "LINKEDIN": "LinkedIn", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "TWITTER": "Twitter", "GITHUB": "GitHub", "GITLAB": "Gitlab", "UPWORK": "Upwork", "STACK_OVERFLOW": "Stackoverflow", "STATUS": "Status", "INVOICE_NUMBER": "Invoice Number", "PROJECT_URL": "Project URL", "CLIENTS": "Clients", "BUDGET_TYPE": "Type", "BUDGET": "Budget", "HOURS": "Hours", "COST": "Cost", "MAKE_COM_CLIENT_ID": "Enter your Make.com Client ID", "MAKE_COM_CLIENT_SECRET": "Enter your Make.com Client Secret", "ZAPIER_CLIENT_ID": "Enter your Zapier Client ID", "ZAPIER_CLIENT_SECRET": "Enter your Zapier Client Secret", "ADD_EDUCATION": {"SCHOOL_NAME": "School name", "DEGREE": "Degree/Diploma", "FIELD_OF_STUDY": "Field(s) of study", "DATE_OF_COMPLETION": "Date of completion", "ADDITIONAL_NOTES": "Additional notes (optional)"}, "ADD_EXPERIENCE": {"OCCUPATION": "Occupation", "ORGANIZATION": "Organization name", "DURATION": "Duration", "DESCRIPTION": "Description (optional)"}, "ADD_INTERVIEW": {"TITLE": "Title", "DATE": "Date", "TIME": "Time", "DURATION": "Duration", "INTERVIEWERS": "Interviewers", "LOCATION": "Location", "NOTE": "Note (optional)", "TYPE": "Interview type", "CALL": "Call", "MEETING": "Meeting"}, "MEETING_AGENDA": "Agenda", "MEETING_LOCATION": "Location", "MEETING_DESCRIPTION": "Description", "BUFFER_TIME": "Buffer minutes", "BREAK_TIME": "Break minutes", "DESCRIPTION": "Description", "DURATION": "Duration", "TITLE": "Title", "SHORT_DESCRIPTION": "Short Description", "EG_FULL_STACK_WEB_DEVELOPER": "E.g. Full-Stack Web Developer", "COORDINATE": {"LATITUDE": "Latitude", "LONGITUDE": "Longitude"}, "SELECT_EXPENSE": "Select Expense", "ADD_TITLE": "Add title", "ALL_PROJECTS": "All Projects", "ALL_TEAMS": "All Teams", "UPWORK_API_KEY": "Upwork API key", "UPWORK_SECRET": "Upwork Secret", "GAUZY_API_KEY": "Gauzy AI Key", "GAUZY_API_SECRET": "Gauzy AI Secret", "OPEN_AI_API_SECRET_KEY": "Open AI Secret Key", "OPEN_AI_ORGANIZATION_ID": "Open AI Organization ID", "SELECT_COMPANY": "Select Company", "TYPE_SEARCH_REQUEST": "Type your search request here...", "SELECT_ICON": "Select Icon", "SELECT": "Select", "SPRINT_LENGTH": "Sprint length", "SPRINT_START_DATE": "Sprint start date", "SPRINT_END_DATE": "Sprint end date", "SPRINT_GOAL": "Sprint goal", "POLICY_NAME": "Policy Name", "SELECT_DATE": "Select Date", "DAYS_UNTIL_DUE": "Days Until Due", "TEMPLATES": "Templates", "INVOICE_TEMPLATE": "Invoice Template", "ESTIMATE_TEMPLATE": "Estimate Template", "RECEIPT_TEMPLATE": "Receipt Template", "INVITATION_EXPIRATION": "Invitation Expiration", "ADD_PROJECT": "Add project", "ADD_TEAM": "Add team", "ADD_EMPLOYEE": "Add employee", "ADD_ORGANIZATION": "Add organization", "DRAG_DROP_FILE": "Drag and Drop the file here", "UPWORK_ORGANIZATION_ID": "Upwork Organization ID", "UPWORK_ORGANIZATION_NAME": "Upwork Organization Name", "UPWORK_ID": "Upwork ID", "LINKEDIN_ID": "LinkedIn ID", "AUTO_SYNC_TASKS": "Auto Sync Tasks", "AUTO_SYNC_TASKS_BASED_ON_LABEL": "Auto-sync Tasks On Label", "AUTO_SYNC_TAG": "Select Auto-sync Label", "SELECT_PROJECT": "Select Project", "ENABLE_JOBS_SEARCH_MATCHING_ANALYSIS": "Enable Jobs Search & Matching Analysis", "ENABLE_EMPLOYEE_PERFORMANCE_ANALYSIS": "Enable Employee Performance Analysis", "STANDARD_WORK_HOURS_PER_DAY": "Enter the number of standard work hours per day (e.g., 8)"}, "RATES": {"DEFAULT_RATE": "Default Rate", "EXPECTED_RATE": "Expected Rate", "LIMITS": "Limits", "ERRORS": {"LIMIT_MAX": "Recurring Weekly Limit (hours) should not be above {{ max }} hours.", "LIMIT_MIN": "Recurring Weekly Limit (hours) should not be under {{ min }} hour.", "BILL_RATE": "Billing Rate can't be under {{ min }}{{ currency}}/hour ", "BILL_RATE_MIN": "Minimum Billing Rate can't be under {{ min }}{{ currency}}/hour "}}, "CHECKBOXES": {"INCLUDE_DELETED": "Show Deleted", "INCLUDE_ARCHIVED": "Include Archived", "ONLY_PAST": "Only Past", "ONLY_FUTURE": "Only Future"}, "NOTIFICATIONS": {"STARTED_WORK_ON": "It's required to enter the date when the employee started work to generate employee payroll, enable the employee to participate in split expenses, see employee statistics"}, "ARCHIVE_CONFIRMATION": {"SURE": "Are you sure you want to archive", "RECORD": "record", "CANDIDATE": "candidate"}, "CANDIDATE_ACTION_CONFIRMATION": {"SURE": "Are you sure you want to ", "RECORD": "record", "CANDIDATE": "candidate", "HIRE": "hire", "REJECT": "reject"}, "DELETE_CONFIRMATION": {"REMOVE_ALL_DATA": "Are you sure you want to remove all data?", "DELETE_ACCOUNT": "Are you sure you want to delete your account?", "REMOVE_USER": " from your organization", "SURE": "Are you sure you want to delete", "RECORD": "record", "USER_RECORD": "from your organization", "EMPLOYEE": "employee", "CANDIDATE": "candidate", "EXPENSE": "Expense", "USER": "user", "INVITATION": "invitation", "DELETE_USER": " from database as it is associated only to current organization", "EVENT_TYPE": "Event type"}, "COUNTDOWN_CONFIRMATION": {"WAS": "was", "ENABLED": "enabled", "DISABLED": "disabled", "WAIT_UNTIL_RELOAD": "Please wait till application reloads"}, "ERROR": {"PROJECT_NAME": "Project name is required.", "PROJECT_URL": "Project url is invalid.", "OPEN_SOURCE_PROJECT_URL": "Open source project url is invalid.", "INVALID_IMAGE_URL": "Invalid image URL."}}, "POP_UPS": {"SELECT_ORGANIZATION": "Please select organizations from the menu above.", "ADD_INCOME": "Add Income", "ADD_EXPENSE": "Add Expense", "EMPLOYEE": "Employee", "EDIT_INCOME": "Edit Income", "EDIT_EXPENSE": "Edit Expense", "EDIT_PAGE": "Edit Page", "SHORT_DESCRIPTION": "Short Description", "OVERVIEW": "Overview", "COMPANY_NAME": "Company Name", "NAME": "Name", "YEAR": "Year", "BANNER": "Banner", "SIZE": "Size", "YEAR_FOUNDED": "Year Founded", "COMPANY_SIZE": "Company Size", "CLIENT_FOCUS": "Client Focus", "DUPLICATE": "Duplicate", "DATE": "Date", "PICK_DATE": "Pick a Date", "ALL_CONTACTS": "All Contacts", "CONTACT": "Contact", "ALL_VENDORS": "Vendor / Merchant", "ALL_CATEGORIES": "All Categories", "CATEGORY_NAME": "Category Name", "EXPENSE_VALUE": "Expense Value", "TAX_LABEL": "Tax Label", "TAX_TYPE": "Tax Type (% or Value)", "TAX_RATE": "Tax Rate", "RECURRING_EXPENSES": "Recurring Monthly Expenses", "PURPOSE": "Purpose", "BACK_TO_WORK": "back to work", "END_WORK": "End Work for", "START_WORK_FOR": "Start work for", "AMOUNT": "Amount", "NOTES": "Notes", "EDIT": "Edit", "ADD": "Add", "DELETE_RECURRING_EXPENSE": "Delete Recurring Expense", "DELETE_ONLY_THIS": "Delete only this", "DELETE_THIS_FUTURE": "Delete this and future", "DELETE_ALL_ENTRIES": "Delete all entries", "CONFIRM": "Confirm", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "Are you sure you want to resend the invite to", "OK": "OK", "CANCEL": "Cancel", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "Are you sure you want to change the", "RECURRING_EXPENSE": "This is a recurring expense", "SPLIT_EXPENSE_WITH_INFO": "This is a split expense with original value: {{ originalValue }}, divided by the number of employees: {{ employeeCount }}", "STARTS_ON": "Starts On", "EXPENSE_HISTORY": "Expense History", "NEW_EXPENSE_VALUE": "New Expense Value", "OFFICIAL_NAME": "This name would be used in tax invoices etc.", "ADD_EVENT_TYPE": "Add Event Type", "EDIT_EVENT_TYPE": "Edit Event Type", "AWARDS": "Awards", "LEVEL": "Level", "LANGUAGES": "Languages", "TOTAL_INCOME_OR_MONTHLY_INCOME": "Total Income/Monthly Income", "PROFITS": "Profits", "BONUSES_PAID": "Bonuses Paid", "TOTAL_HOURS_WORKED_OVER_GAUZY": "Total Hours worked over Gauzy", "MINIMUM_PROJECT_SIZE": "Minimum Project Size", "PROJECTS_COUNT": "Projects Count", "CLIENTS_COUNT": "Clients Count", "EMPLOYEES_COUNT": "Employees Count", "DETAILS": "Details", "SKILLS": "Skills", "PRIVACY": "Privacy", "SELECT_TIMEZONE": "Select Timezone", "SHOW_AVERAGE_BONUS": "Show Average Bonus", "SHOW_AVERAGE_INCOME": "Show Average Income", "SHOW_PAYPERIOD": "Show Pay Period", "SHOW_ANONYMOUS_BONUS": "Show Anonymous Bonus", "SHOW_AVERAGE_EXPENSES": "Show Average Expenses", "SHOW_BILLRATE": "Show Bill Rate", "SHOW_START_WORK_ON": "Show Started Work On", "SHOW_CLIENTS": "Show Clients", "DISPLAY_BONUS_ANONYMOUSLY": "Display bonus Anonymously", "SOURCE": "Source", "DESCRIPTION": "Description", "MAIN": "Main", "CATEGORIES": "Categories", "ADD_LANGUAGE": "Add language", "REGISTER_AS_EMPLOYEE_TOOLTIP": "You must be employee in order to be able to track time, create split expenses and use other employees related features."}, "MENU": {"PIPELINES": "Pipelines", "DASHBOARD": "Dashboard", "DASHBOARDS": "Dashboards", "APPOINTMENTS": "Appointments", "FAVORITES": "Favorites", "ACCOUNTING": "Accounting", "INCOME": "Income", "EXPENSES": "Expenses", "RECURRING_EXPENSE": "Recurring Expenses", "POSITIONS": "Positions", "INTEGRATIONS": "Apps & Integrations", "UPWORK": "Upwork", "MAKE_COM": "Make.com", "ZAPIER": "Zapier", "GAUZY_AI": "Gauzy AI", "PROPOSALS": "Proposals", "TIME_OFF": "Time Off", "APPROVALS": "Approvals", "HELP": "Help", "ABOUT": "About", "CONTACTS": "Contacts", "ADMIN": "Admin", "EMPLOYEE_LEVEL": "Employee Level", "EMPLOYEES": "Employees", "MANAGE": "Manage", "CANDIDATES": "Candidates", "ORGANIZATIONS": "Organizations", "SETTINGS": "Settings", "GENERAL": "General", "EMAIL_HISTORY": "Email History", "USERS": "Users", "ROLES": "Roles & Permissions", "DANGER_ZONE": "Danger Zone", "FILE_STORAGE": "File Storage", "INVITE_PEOPLE": "Invite People", "IMPORT_EXPORT": {"IMPORT_EXPORT": "Import/Export", "IMPORT_EXPORT_DATA": "Import / Export Data", "IMPORT": "Import", "IMPORT_HISTORY": "Import History", "IMPORT_DATE_TIME": "Imported Date & Time", "EXPORT": "Export", "MERGE": "Merge data", "CLEAN_UP": "Clean up before import", "EXPORT_MESSAGE": "Click \"EXPORT\" to download all tables from the database. ", "IMPORT_MESSAGE": "Click \"IMPORT\" to insert all tables in the database. The name of the file for upload should be: \"archive.zip\". Choose also between options \"Merge data\" and \"Clean up before import\".", "IMPORT_HISTORY_MESSAGE": "You can track your recently imported files here", "SELECT_FILE": "Select File", "DROP_FILE": "Drop the file here", "NO_DROP_FILE": "Can't drop the file here", "BROWSE": "BROWSE", "NAME": "Name", "SIZE": "Size", "PROGRESS": "Progress", "STATUS": "Status", "ACTIONS": "Actions", "QUEUE_PROGRESS": "Queue progress:", "CANCEL": "Cancel", "REMOVE": "Remove", "WRONG_FILE_NAME": "Wrong file name!!!", "CORRECT_FILE_NAME": "The file name should be: \"archive.zip\"", "DOWNLOAD_TEMPLATES": "Download templates", "MIGRATE_TO_CLOUD": "Migrate to <PERSON>", "IMPORT_INFO": "Click \"Import\" to import your data into Gauzy DB.", "EXPORT_INFO": "Click \"Export\" to export data from Gauzy DB.", "DOWNLOAD_TEMPLATES_INFO": "Click \"Download Templates\" to download Zip archive with CSV templates for the format of Gauzy data.", "MIGRATE_TO_CLOUD_INFO": "Click \"Migrate to Ever Gauzy Cloud\" to migrate from self-hosted to cloud edition.", "MIGRATE_SUCCESSFULLY": "Gauzy Cloud migration for '{{ tenant }}' successfully!", "ACCOUNTING_TEMPLATE": "Accounting Template", "ACTIVITY": "Activity", "APPROVAL_POLICY": "Approval Policy", "AVAILABILITY_SLOT": "Availability Slot", "CANDIDATE": "Candidate", "CONTACT": "Contact", "COUNTRY": "Country", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "DEAL": "Deal", "EMAIL": "Email", "EMPLOYEE": "Employee", "EQUIPMENT": "Equipment", "EVENT_TYPES": "Event Types", "EXPENSE": "Expense", "GOAL": "Goal", "INCOME": "Income", "INTEGRATION": "Integration", "INVITE": "Invite", "INVOICE": "Invoice", "JOB": "Job", "KEY_RESULT": "Key Results", "KNOWLEDGE_BASE": "Knowledge Base", "LANGUAGE": "Language", "MERCHANT": "Merchant", "ORGANIZATION": "Organization", "PAYMENT": "Payment", "PIPELINE": "Pipeline", "PIPELINE_STAGE": "Pipeline Stage", "PRODUCT": "Product", "PROPOSAL": "Proposal", "REPORT": "Report", "REQUEST_APPROVAL": "Request Approval", "ROLE": "Role", "SKILL": "Skill", "TAG": "Tag", "TASK": "Task", "TENANT": "Tenant", "TENANT_SETTING": "Tenant Setting", "TIME_OFF_POLICY": "Time off policy", "TIME_SHEET": "Time Sheet", "USER": "User", "CANDIDATE_CRITERION_RATING": "Candidate Criterion Rating", "CANDIDATE_DOCUMENT": "Candidate Document", "CANDIDATE_EDUCATION": "Candidate Education", "CANDIDATE_EXPERIENCE": "Candidate Experience", "CANDIDATE_FEEDBACK": "Candidate <PERSON><PERSON><PERSON>", "CANDIDATE_INTERVIEW": "Candidate Interview", "CANDIDATE_INTERVIEWER": "Candidate Interviewer", "CANDIDATE_PERSONAL_QUALITY": "Candidate Personal Quality", "CANDIDATE_SKILL": "Candidate <PERSON><PERSON>", "CANDIDATE_SOURCE": "Candidate Source", "CANDIDATE_TECHNOLOGY": "Candidate Technology", "ORGANIZATION_AWARD": "Organization Award", "ORGANIZATION_CONTACT": "Organization Contact", "ORGANIZATION_DEPARTMENT": "Organization Department", "ORGANIZATION_DOCUMENT": "Organization Document", "ORGANIZATION_EMPLOYEE_LEVEL": "Organization Employee Level", "ORGANIZATION_EMPLOYMENT_TYPE": "Organization Employment Type", "ORGANIZATION_LANGUAGES": "Organization Languages", "ORGANIZATION_POSITION": "Organization Position", "ORGANIZATION_PROJECT": "Organization Project", "ORGANIZATION_RECURRING_EXPENSE": "Organization Recurring Expense", "ORGANIZATION_SPRINT": "Organization Sprint", "ORGANIZATION_TEAM": "Organization Team", "ORGANIZATION_TEAM_EMPLOYEE": "Organization Team Employee", "ORGANIZATION_VENDOR": "Organization Vendor", "EMAIL_TEMPLATE": "<PERSON>ail Te<PERSON>late", "ESTIMATE_EMAIL": "Estimate Email", "EMPLOYEE_APPOINTMENT": "Employee Appointment", "EMPLOYEE_AWARD": "Employee Award", "EMPLOYEE_PROPOSAL_TEMPLATE": "Employee Proposal Template", "EMPLOYEE_RECURRING_EXPENSE": "Employee Recurring Expense", "EMPLOYEE_SETTING": "Employee Setting", "EMPLOYEE_UPWORK_JOB_SEARCH_CRITERION": "Employee Upwork Job Search Criterion", "INTEGRATION_ENTITY_SETTING": "Integration Entity Setting", "INTEGRATION_ENTITY_SETTING_TIED_ENTITY": "Integration Entity Setting Tied Entity", "INTEGRATION_MAP": "Integration Map", "INTEGRATION_SETTING": "Integration Setting", "INTEGRATION_TENANT": "Integration Tenant", "INTEGRATION_TYPE": "Integration Type", "INVITE_ORGANIZATION_CONTACT": "Invite Organization Contact", "INVITE_ORGANIZATION_DEPARTMENT": "Invite Organization Department", "INVITE_ORGANIZATION_PROJECT": "Invite Organization Project", "PRODUCT_CATEGORY": "Product Category", "PRODUCT_CATEGORY_TRANSLATION": "Product Category Translation", "PRODUCT_GALLERY_ITEM": "Product Gallery Item", "PRODUCT_OPTION": "Product Option", "PRODUCT_OPTION_GROUP": "Product Option Group", "PRODUCT_OPTION_GROUP_TRANSLATION": "Product Option Group Translation", "PRODUCT_OPTION_TRANSLATION": "Product Option Translation", "PRODUCT_STORE": "Product Store", "PRODUCT_TRANSLATION": "Product Translation", "PRODUCT_TYPE": "Product Type", "PRODUCT_TYPE_TRANSLATION": "Product Type Translation", "PRODUCT_VARIANT": "Product Variant", "PRODUCT_VARIANT_PRICE": "Product Variant Price", "PRODUCT_VARIANT_SETTING": "Product Variant Setting", "REPORT_CATEGORY": "Report Category", "REPORT_ORGANIZATION": "Report Organization", "REQUEST_APPROVAL_TAG": "Request Approval Tag", "REQUEST_APPROVAL_EMPLOYEE": "Request Approval Employee", "REQUEST_APPROVAL_TEAM": "Request Approval Team", "SKILL_EMPLOYEE": "<PERSON><PERSON> Employee", "SKILL_ORGANIZATION": "Skill Organization", "TAG_CANDIDATE": "Tag Candidate", "TAG_EMPLOYEE": "Tag Employee", "TAG_EQUIPMENT": "Tag Equipment", "TAG_EVENT_TYPE": "Tag Event Type", "TAG_EXPENSE": "Tag Expense", "TAG_INCOME": "Tag Income", "TAG_INVOICE": "Tag Invoice", "TAG_ORGANIZATION_CONTACT": "Tag Organization Contact", "TAG_ORGANIZATION_DEPARTMENT": "Tag Organization Department", "TAG_ORGANIZATION_EMPLOYEE_LEVEL": "Tag Organization Employee Level", "TAG_ORGANIZATION_EMPLOYEE_TYPE": "Tag Organization Employee Type", "TAG_ORGANIZATION_EXPENSES_CATEGORY": "Tag Organization Expense Category", "TAG_ORGANIZATION_POSITION": "Tag Organization Position", "TAG_ORGANIZATION_PROJECT": "Tag Organization Project", "TAG_ORGANIZATION_TEAM": "Tag Organization Team", "TAG_ORGANIZATION_VENDOR": "Tag Organization Vendor", "TAG_ORGANIZATIONS": "Tag Organizations", "TAG_PAYMENT": "Tag Payments", "TAG_PRODUCT": "Tag Product", "TAG_PROPOSAL": "Tag Proposal", "TAG_TASK": "Tag Task", "TAG_USER": "Tag User", "TASK_EMPLOYEE": "Tag Employee", "TASK_TEAM": "Tag Team", "EQUIPMENT_SHARING": "Equipment Sharing", "EQUIPMENT_SHARE_POLICY": "Equipment Share Policy", "EXPENSE_CATEGORY": "Expense Category", "GOAL_KPI": "Goal Kpi", "GOAL_GENERAL_SETTING": "Goal General Setting", "GOAL_KPI_TEMPLATE": "Goal Kpi Template", "GOAL_TEMPLATE": "Goal Template", "GOAL_TIME_FRAME": "Goal Time Frame", "KNOWLEDGE_BASE_ARTICLE": "Knowledge Base Article", "KNOWLEDGE_BASE_AUTHOR": "Knowledge Base Author", "INVOICE_ESTIMATE_HISTORY": "Invoice Estimate History", "INVOICE_ITEM": "Invoice Item", "JOB_PRESET": "Job Preset", "JOB_PRESET_UPWORK_SEARCH_CRITERION": "Job Preset Upwork Search Criterion", "JOB_SEARCH_OCCUPATION": "Job Search Occupation", "JOB_SEARCH_CATEGORY": "Job Search Category", "KEY_RESULT_TEMPLATE": "Key Result Template", "KEY_RESULT_UPDATE": "Key Result Update", "ROLE_PERMISSION": "Role Permission", "TIME_OFF_POLICY_EMPLOYEE": "Time Off Policy Employee", "TIME_OFF_REQUEST": "Time Off Request", "TIME_OFF_REQUEST_EMPLOYEE": "Time Off Request Employee", "SCREENSHOT": "Screenshot", "TIME_LOG": "Time Log", "TIME_SLOT": "Time Slot", "TIME_SLOT_MINUTES": "Time Slot Minutes", "TIME_SLOT_TIME_LOGOS": "Time Slot Time Logs", "USER_ORGANIZATION": "User Organization", "WAREHOUSE": "Warehouse", "WAREHOUSE_MERCHANT": "Warehouse Merchant", "WAREHOUSE_PRODUCT": "Warehouse Product", "WAREHOUSE_PRODUCT_VARIANT": "Warehouse Product Variant", "WAREHOUSE_STORE": "Warehouse Store", "PRODUCT_IMAGE_ASSET": "Product Image Asset", "CUSTOM_SMTP": "Custom SMTP", "FEATURE": "Feature", "FEATURE_ORGANIZATION": "Feature Organization", "EXPORT_DATA": "Export Data", "IMPORT_DATA": "Import Data", "ALL_ENTITIES": "ALL Entities", "ACTIVITY_LOG": "Activity log", "DAILY_PLAN": "Daily plan", "ENTITY_SUBSCRIPTION": "Entity subscription", "FAVORITE": "Favorite", "MENTION": "Mention", "REACTION": "Reaction", "RESOURCE_LINK": "Resource link", "SCREENING_TASK": "Screening task", "SOCIAL_ACCOUNT": "Social account", "CHANGELOG": "Changelog", "ISSUE_TYPE": "Issue type", "TASK_ESTIMATION": "Task estimation", "TASK_LINKED_ISSUE": "Task linked issue", "TASK_PRIORITY": "Task priority", "TASK_RELATED_ISSUE": "Task related issue", "TASK_SIZE": "Task size", "TASK_STATUS": "Task status", "TASK_VERSION": "Task version", "TASK_RELATED_ISSUE_TYPE": "Task related issue type", "ORGANIZATION_PROJECT_EMPLOYEE": "Organization project employee", "ORGANIZATION_PROJECT_MODULE": "Organization project module", "ORGANIZATION_PROJECT_MODULE_EMPLOYEE": "Organization project module employee", "ORGANIZATION_SPRINT_EMPLOYEE": "Organization sprint employee", "ORGANIZATION_SPRINT_TASK": "Organization sprint task", "ORGANIZATION_SPRINT_TASK_HISTORY": "Organization sprint task history", "ORGANIZATION_TASK_SETTING": "Organization task setting", "ORGANIZATION_TEAM_JOIN_REQUEST": "Organization team join request", "ORGANIZATION_GITHUB_REPOSITORY": "Organization GitHub repository", "ORGANIZATION_GITHUB_REPOSITORY_ISSUE": "Organization GitHub repository issue", "EMPLOYEE_AVAILABILITY": "Employee availability", "EMPLOYEE_NOTIFICATION": "Employee notification", "EMPLOYEE_PHONE": "Employee phone", "APPOINTMENT_EMPLOYEE": "Appointment employee", "EMPLOYEE_NOTIFICATION_SETTING": "Employee notification setting", "PRODUCT_REVIEW": "Product review", "TASK_LINKED_ISSUES": "Linked Issues", "TASK_VIEW": "Task View"}, "TAGS": "Tags", "LANGUAGE": "Language", "LANGUAGES": "Languages", "EQUIPMENT": "Equipment", "EQUIPMENT_SHARING": "Equipment Sharing", "TASKS": "Tasks", "TASKS_SETTINGS": "Settings", "INVOICES": "Invoices", "ORGANIZATION": "Organization", "TENANT": "Tenant", "RECURRING_INVOICES": "Invoices Recurring", "INVOICES_RECEIVED": "Invoices Received", "ESTIMATES_RECEIVED": "Estimates Received", "ESTIMATES": "Estimates", "MY_TASKS": "My Tasks", "JOBS": "Jobs", "PROPOSAL_TEMPLATE": "Proposal Template", "JOBS_SEARCH": "Browse", "JOBS_MATCHING": "Matching", "TEAM_TASKS": "Team's Tasks", "TIME_ACTIVITY": "Time & Activity", "TIMESHEETS": "Timesheets", "SCHEDULES": "Schedules", "EMAIL_TEMPLATES": "Email Templates", "REPORTS": "Reports", "GOALS": "Goals", "ALL_REPORTS": "All Reports", "TIME_REPORTS": "Time Report", "WEEKLY_TIME_REPORTS": "Weekly Report", "ACCOUNTING_REPORTS": "Accounting Reports", "PAYMENT_GATEWAYS": "Payment Gateways", "SMS_GATEWAYS": "SMS Gateways", "CUSTOM_SMTP": "Custom SMTP", "INVENTORY": "Inventory", "SALES": "Sales", "PAYMENTS": "Payments", "FEATURES": "Features", "ACCOUNTING_TEMPLATES": "Accounting Templates", "FOCUS": "Focus", "APPLICATIONS": "Applications", "OPEN_GA_BROWSER": "Open Gauzy In Browser", "START_SERVER": "Start Server", "STOP_SERVER": "Stop Server"}, "SETTINGS_MENU": {"THEMES": "Themes", "LIGHT": "Light", "DARK": "Dark", "COSMIC": "Cosmic", "CORPORATE": "Corporate", "MATERIAL_LIGHT_THEME": "Material Light", "MATERIAL_DARK_THEME": "Material Dark", "GAUZY_LIGHT": "Gauzy Light", "GAUZY_DARK": "Gauzy <PERSON>", "LANGUAGE": "Language", "ENGLISH": "English", "FRENCH": "French", "SPANISH": "Spanish", "BULGARIAN": "Bulgarian", "HEBREW": "Hebrew", "RUSSIAN": "Russian", "CHINESE": "Chinese", "GERMAN": "German", "PORTUGUESE": "Portuguese", "ITALIAN": "Italian", "DUTCH": "Dutch", "POLISH": "Polish", "ARABIC": "Arabic", "PREFERRED_LAYOUT": "Layout", "PREFERRED_LAYOUT_TOOLTIP": "This will only set the default layout and not change the layout on each page if you have already changed it once.", "RESET_LAYOUT": "Reset Layout", "RESET_LAYOUT_TOOLTIP": "Reset layout to default for all pages", "TABLE": "Table", "CARDS_GRID": "Cards Grid", "SPRINT_VIEW": "Sprint View", "QUICK_SETTINGS": "Quick Settings", "NO_LAYOUT": "No data layout available"}, "CHANGELOG_MENU": {"HEADER": "What's new?", "LEARN_MORE_URL": "Learn more", "GAUZY_FEATURES": "Gauzy features"}, "REPORT_PAGE": {"MEMBERS_WORKED": "Members Worked", "MANUAL_TIME_EDIT_REPORT": "Manual Time Edit Report", "GROUP_BY": "Group", "DATE": "Date", "TO_DO": "To Do", "TIME": "Time", "PROJECT": "Project", "CLIENT": "Client", "NOTES": "Notes", "PROJECTS_WORKED": "Projects Worked", "APPS_AND_URLS_REPORT": "Apps & URLs Report", "ACTIVITY": "Activity", "TOTAL_HOURS": "Total Hours", "EMPLOYEE": "Employee", "EMPLOYEES/TEAMS": "Employees /Teams", "NO_PROJECT": "No Project", "NO_EMPLOYEE": "No Employee", "TITLE": "Title", "ACTION": "Action", "TIME_SPAN": "Time Span", "REASON": "Reason", "CHANGED_AT": "Changed at", "DURATION": "Duration", "NO_TASK": "No Task", "FROM": "From", "WEEKLY_TIME_AND_ACTIVITY_REPORT": "Weekly Time and Activity Report", "TIME_AND_ACTIVITY_REPORT": "Time and Activity Report", "NO_CLIENT": "No Client", "ALL_REPORTS": "All Reports", "EXPENSES_REPORT": "Expenses Report", "CATEGORY": "Category", "DESCRIPTION": "Description", "AMOUNT": "Amount", "NO_EXPENSES": "No Expenses", "PAYMENT_REPORT": "Payments Report", "NO_PAYMENTS": "No Payments", "CONTACT": "Contact", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "NOTE": "Note", "AMOUNT_OWED": "Amount Owed Report", "CURRENT_RATE": "Current Rate", "HOURS": "Hours", "SPENT": "Spent", "BUDGET": "Budget", "REMAINING": "Remaining", "WEEKLY_LIMIT_REPORT": "Weekly Limit Report", "NO_EMPLOYEES": "No Employees", "DAILY_LIMIT_REPORT": "Daily Limit Report", "LIMIT": "Limit", "SPENT_HOURS": "Spent Hours", "REMAINING_HOURS": "Remaining Hours", "PROJECT_BUDGET_REPORTS": "Project Budget Reports", "CLIENT_BUDGET_REPORTS": "Client Budget Reports", "EXPENSE": "Expense", "PAYMENT": "Payment", "NO_EMPLOYEES_WORKED": "No employees worked", "WEEKLY_TOTAL": "Weekly Total", "NO_DATA": {"APP_AND_URL_ACTIVITY": "No records found. Please select date range, employee or project.", "MANUAL_ACTIVITY": "No records found. Please select date range, employee or project.", "AMOUNT_OWED": "No Amount Owed", "WEEKLY_TIME_AND_ACTIVITY": "You have not any tracked time and activity yet for these day's.", "DAILY_TIME_AND_ACTIVITY": "There is no time and activity tracked yet for this day. Please use the <a href={{downloadURL}} rel=\"noopener\" target=\"_blank\">Gauzy Desktop Timer App</a>.", "PROJECT_BUDGET": "You have not created any project with budget.", "CLIENT_BUDGET": "You have not created any client with budget."}, "STANDARD_WORK_HOURS": "Standard Work Hours ({{ hours }})"}, "INTEGRATIONS": {"TITLE": "Integrations", "AVAILABLE_INTEGRATIONS": "Available Apps & Integrations", "ADDED_UPWORK_TRANSACTION": "Added Upwork Transaction", "TOTAL_UPWORK_TRANSACTIONS_SUCCEED": "Total expense transactions succeed: {{ totalExpenses }}. Total income transactions succeed: {{ totalIncomes }}", "HUBSTAFF_PAGE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "SELECT_ORGANIZATION": "Select Organization", "SYNCED_PROJECTS": "Projects synced", "SETTINGS_UPDATED": "Updated settings for integration", "SYNCED_ENTITIES": "Auto synced entities", "TOOLTIP_ACTIVITY_INFO": "Date range Limit: 7 days Earliest Date: 6 full months", "DATE_RANGE_PLACEHOLDER": "Choose date between", "CLIENT_ID": "Hubstaff Client ID", "CLIENT_SECRET": "Hubstaff Client Secret", "GRANT_PERMISSION": "Next you will be taken to Hubstaff to grant permission to <PERSON><PERSON><PERSON>.", "ENTER_CLIENT_SECRET": "Enter client secret to get access token.", "DESCRIPTION": "Activate Hubstaff integration for enhanced workforce management."}, "UPWORK_PAGE": {"ACTIVITIES": "Activities", "REPORTS": "Reports", "TRANSACTIONS": "Transactions", "SUCCESSFULLY_AUTHORIZED": "Successfully authorized", "API_KEY": "Upwork API key", "SECRET": "Upwork Secret", "NEXT_STEP_INFO": "Next you will be taken to Upwork to grant permission to <PERSON><PERSON><PERSON>.", "CONTRACTS": "Contracts", "SYNCED_CONTRACTS": "Synced Contracts", "SELECT_DATE": "Select Date", "ONLY_CONTRACTS": "Only Contracts", "CONTRACTS_RELATED_DATA": "Synced Contracts related entities", "DATE_RANGE_PLACEHOLDER": "Choose date between", "HOURLY": "Hourly", "DESCRIPTION": "Activate Upwork integration for freelance workforce management."}, "MAKE_COM_PAGE": {"SCENARIOS": "Scenarios", "EXECUTIONS": "Executions", "HISTORY": "History", "SETTINGS": "Settings", "ERRORS": {"INVALID_FORM": "Please fill in all required fields", "START_AUTHORIZATION": "Please start the authorization process"}, "CLIENT_ID": "Client ID", "CLIENT_SECRET": "Client Secret", "NEXT_STEP_INFO": "After authorization, you will be redirected to Make.com to complete the integration setup"}, "MAKE_COM": {"SETTINGS": {"TITLE": "Make.com Settings", "ENABLE_INTEGRATION": "Enable Make.com Integration", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_URL_PLACEHOLDER": "Enter webhook URL", "WEBHOOK_URL_INVALID": "Please enter a valid webhook URL", "SAVE": "Save Settings"}}, "ZAPIER_PAGE": {"CLIENT_ID": "Client ID", "CLIENT_SECRET": "Client Secret", "NEXT_STEP_INFO": "After authorization, you will be redirected to Zapier to complete the integration setup", "ERRORS": {"LOAD_SETTINGS": "Failed to load Zapier settings", "SAVE_SETTINGS": "Failed to save Zapier settings", "INVALID_FORM": "Please fill in all required fields", "MISSING_CREDENTIALS": "Missing client credentials", "NO_ACCESS_TOKEN": "No access token available", "LOAD_WEBHOOKS": "Failed to load webhooks", "DELETE_WEBHOOK": "Failed to delete webhook", "LOAD_ACTIONS": "Failed to load actions", "LOAD_TRIGGERS": "Failed to load triggers", "START_AUTHORIZATION": "Please start the authorization process", "INVALID_TOKEN": "Invalid token provided", "TOKEN_NOT_FOUND": "Token not found"}, "SUCCESS": {"SETTINGS_SAVED": "Zapier settings saved successfully", "DELETE_WEBHOOK": "Webhook deleted successfully"}, "TAB": {"TRIGGERS": "Triggers", "ACTIONS": "Actions", "WEBHOOKS": "Webhooks", "SETTINGS": "Settings"}, "TOOLTIP": {"TRIGGERS": "Triggers are events that occur in Gauzy and can be used to trigger actions in Zapier.", "ACTIONS": "Actions are events that occur in Zapier and can be used to trigger actions in Gauzy.", "WEBHOOKS": "Webhooks are events that occur in Zapier and can be used to trigger actions in Gauzy.", "SETTINGS": "Settings are the settings for the Zapier integration."}, "MESSAGE": {"TRIGGERS_ENABLED": "Triggers have been successfully enabled.", "TRIGGERS_DISABLED": "Triggers have been successfully disabled.", "ACTIONS_ENABLED": "Actions have been successfully enabled.", "ACTIONS_DISABLED": "Actions have been successfully disabled.", "WEBHOOKS_ENABLED": "Webhooks have been successfully enabled.", "WEBHOOKS_DISABLED": "Webhooks have been successfully disabled."}, "SETTINGS": {"TITLE": "Zapier Settings", "ENABLE_INTEGRATION": "Enable Zapier Integration", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_URL_PLACEHOLDER": "Enter webhook URL", "WEBHOOK_URL_INVALID": "Please enter a valid webhook URL", "WEBHOOK_INFO": "Webhook is a URL that is used to update data in Gauzy based on a specific event in Zapier.", "SAVE": "Save Settings"}, "ACTIONS": {"TITLE": "Actions", "NO_ACTIONS": "No actions available"}, "TRIGGERS": {"TITLE": "Triggers", "NO_TRIGGERS": "No triggers available"}, "WEBHOOKS": {"TITLE": "Webhooks", "NO_WEBHOOKS": "No webhooks configured"}}, "GAUZY_AI_PAGE": {"TITLE": "Gauzy AI Integration", "API_KEY": "Gauzy AI key", "API_SECRET": "Gauzy AI Secret", "OPEN_AI_API_SECRET_KEY": "Open AI Secret Key", "OPEN_AI_ORGANIZATION_ID": "Open AI Organization ID", "DESCRIPTION": "Activate Gauzy AI integration for smarter job hunting.", "CONSUMER_KEYS": "Consumer Keys", "OPEN_AI_API_KEYS": "Open AI API Keys", "GENERATED": "Generated", "TAB": {"KEYS": "Keys", "SETTINGS": "Settings"}, "TOOLTIP": {"API_KEY": "The API Key serves as your App identifier for API requests, remaining permanently hidden, with some characters visible.", "API_SECRET": "The API Secret serves as your App identifier for API requests, remaining permanently hidden, with some characters visible.", "OPEN_AI_API_SECRET_KEY": "The OpenAI API Secret serves as your App identifier for API requests, remaining permanently hidden, with some characters visible.", "OPEN_AI_ORGANIZATION_ID": "An optional identifier for organizational purposes when interacting with the OpenAI API. It helps organize and distinguish between different entities or projects within your organization.", "ENABLE_JOBS_SEARCH_MATCHING_ANALYSIS": "Enables advanced job search and matching analyses for enhanced accuracy. Disabling hides 'Browse' and 'Matching' menu items for a streamlined interface.", "ENABLE_EMPLOYEE_PERFORMANCE_ANALYSIS": "Manages the transmission of employee work metrics, including keyboard inputs, mouse movements, and screenshots, for analysis by Gauzy AI. Enable for comprehensive performance analysis; disable for strict user privacy and data control."}, "MESSAGE": {"JOBS_SEARCH_MATCHING_ENABLED": "Jobs Search & Matching Analysis has been successfully enabled.", "JOBS_SEARCH_MATCHING_DISABLED": "Jobs Search & Matching Analysis has been successfully disabled.", "EMPLOYEE_PERFORMANCE_ENABLED": "Employee Performance Analysis has been successfully enabled.", "EMPLOYEE_PERFORMANCE_DISABLED": "Employee Performance Analysis has been successfully disabled."}}, "GITHUB_PAGE": {"TITLE": "GitHub Integration", "AUTO_SYNC_TABLE_LABEL": "Syncing GitHub Repositories and Projects", "SELECT_REPOSITORY": "Select repository", "SEARCH_REPOSITORY": "Type to search repository", "SYNCED_ISSUES": "'{{repository}}' issues & labels synced successfully", "HAS_SYNCED_ENABLED": "'{{repository}}' sync successfully enabled", "HAS_SYNCED_DISABLED": "'{{repository}}' sync successfully disabled", "DESCRIPTION": "Activate GitHub integration for project & repository sync.", "TAB": {"AUTO_SYNC": "Auto sync", "MANUAL_SYNC": "Manual sync"}}, "COMING_SOON": "Coming soon", "RE_INTEGRATE": "Re-integrate", "SETTINGS": "Settings", "SELECT_GROUPS": "Select Groups", "FILTER_INTEGRATIONS": "Filter integrations", "SEARCH_INTEGRATIONS": "Search integrations", "PAID": "Paid", "INTEGRATION": "Integration", "MESSAGE": {"SETTINGS_UPDATED": "Updated settings for '{{provider}}' integration", "INTEGRATION_DELETED": "Integration '{{provider}}' deleted successfully", "NO_INTEGRATIONS": "You have not configured any integrations", "INTEGRATION_ENABLED": "'{{provider}}' integration successfully enabled", "INTEGRATION_DISABLED": "'{{provider}}' integration successfully disabled", "INTEGRATION_ADDED": "Integration '{{provider}}' has been added to '{{organization}}'"}, "ENABLED": "Enabled", "DISABLED": "Disabled"}, "DASHBOARD_PAGE": {"ACCOUNTING": "Accounting", "HUMAN_RESOURCES": "Human Resources", "TIME_TRACKING": "Time Tracking", "PROJECT_MANAGEMENT": "Project Management", "EMPLOYEE_STATISTICS": "Employee Statistics", "SELECT_A_MONTH_AND_EMPLOYEE": "Please select a month and employee from the menu above", "INSERT_TEXT_FOR_NOT_AUTHENTICATED_USERS": "Insert text for not authenticated users", "CHARTS": {"BAR": "Bar", "DOUGHNUT": "Doughnut", "STACKED_BAR": "Stacked Bar", "CHART_TYPE": "Chart Type", "REVENUE": "Revenue", "EXPENSES": "Expenses", "PROFIT": "Profit", "BONUS": "Bonus", "NO_MONTH_DATA": "No Data for this month", "CASH_FLOW": "Cash Flow", "WORKING": "Working", "WORKING_NOW": "Working now", "NOT_WORKING": "Not working", "WORKING_TODAY": "Working today"}, "PROFIT_HISTORY": {"PROFIT_REPORT": "Profit Report", "TOTAL_EXPENSES": "Total Expenses", "TOTAL_INCOME": "Total Income", "TOTAL_PROFIT": "Total Profit", "DATE": "Date", "EXPENSES": "Expenses", "INCOME": "Income", "DESCRIPTION": "Description"}, "TITLE": {"PROFIT_REPORT": "Profit Report", "TOTAL_EXPENSES": "Total Expenses", "TOTAL_INCOME": "Total Income", "PROFIT": "Profit", "TOTAL_BONUS": "Total Bonus", "TOTAL_DIRECT_INCOME": "Direct Income", "SALARY": "Salary", "TOTAL_DIRECT_INCOME_INFO": "Income from direct bonus", "TOTAL_INCOME_CALC": "Total Income = Income {{ totalNonBonusIncome }} + Direct Income {{ totalBonusIncome }}", "TOTAL_PROFIT_BONUS": "Total Profit Bonus", "TOTAL_DIRECT_BONUS": "Direct Income Bonus", "TOTAL_DIRECT_BONUS_INFO": "This is equal to the direct income", "TOTAL_PROFIT_BONUS_INFO": "{{ bonusPercentage }}% of the profit {{ difference }}", "TOTAL_INCOME_BONUS": "Total Income Bonus", "TOTAL_INCOME_BONUS_INFO": "{{ bonusPercentage }}% of the income {{ totalIncome }}", "TOTAL_EXPENSE_CALC": "Total = Employee Expenses + Split Expenses + Recurring + Salary", "TOTAL_EXPENSES_WITHOUT_SALARY": "Total Expense without salary", "TOTAL_EXPENSES_WITHOUT_SALARY_CALC": "Expense = Employee Expenses + Split Expenses + Recurring", "TOTAL_BONUS_CALC": "Total Bonus = Direct Income Bonus {{ totalBonusIncome }} + Bonus {{ calculatedBonus }}"}, "DEVELOPER": {"DEVELOPER": "Developer", "AVERAGE_BONUS": "Average Monthly Bonus", "TOTAL_INCOME": "Total Income", "TOTAL_EXPENSES": "Total Expenses", "PROFIT": "Profit", "PROFIT_CALC": "Profit (Net Income) = Total Income {{ totalAllIncome }} - Total Expenses {{ totalExpense }}", "NOTE": "Note: negative bonuses should be deducted in the upcoming months from the positive bonuses before final bonus payments", "BONUS": "Bonus", "EMPLOYEES": "Employees"}, "ADD_INCOME": "Add New Income Entry", "ADD_EXPENSE": "Add New Expense Entry", "RECURRING_EXPENSES": "Recurring Expenses", "ADD_ORGANIZATION_RECURRING_EXPENSE": "Add New Recurring Expense Entry for Organization", "ADD_EMPLOYEE_RECURRING_EXPENSE": "Add New Recurring Expense Entry for Employee", "PLAN_MY_DAY": "Plan My Day", "ADD_TODO": "Add <PERSON>", "MOST_VIEW_PROJECTS": "Most Viewed Projects", "INBOX": "Inbox", "RECENTLY_ASSIGNED": "Recently Assigned", "NO_TODO_ASSIGNED": "No Todo Assigned"}, "INCOME_PAGE": {"INCOME": "Income", "BONUS_HELP": "If set, company % fee will NOT be applied to the Bonus Income", "BONUS_TOOLTIP": "This is a direct bonus", "EMPLOYEES_GENERATE_INCOME": "Employees that generate income"}, "EXPENSES_PAGE": {"EXPENSES": "Expenses", "MUTATION": {"CONTACT_IS_REQUIRED": "Contact is required!", "PLEASE_SELECT_A_CONTACT_OR_CHANGE_EXPENSE_TYPE": "Please select a Contact from the dropdown menu below or change Expense type setting.", "ASSIGN_TO": "Assign to", "INCLUDE_TAXES": "Include Taxes", "ATTACH_A_RECEIPT": "Attach a receipt", "EMPLOYEES_GENERATE_EXPENSE": "Employees that generate expense", "TAX_DEDUCTIBLE": "Tax Deductible", "NOT_TAX_DEDUCTIBLE": "Not Tax Deductible", "BILLABLE_TO_CONTACT": "Billable to Contact", "PERCENTAGE": "Percentage", "VALUE": "Value", "TAX_AMOUNT": "Tax Amount", "TAX_RATE": "Tax Rate", "INVOICED": "Invoiced", "UNINVOICED": "Uninvoiced", "PAID": "Paid", "NOT_BILLABLE": "Not Billable"}, "DEFAULT_CATEGORY": {"SALARY": "Salary", "SALARY_TAXES": "Salary Taxes", "RENT": "Rent", "EXTRA_BONUS": "Extra Bonus"}, "SPLIT_HELP": "If set, this expense will be equally divided amongst all employees of the company & will be considered in each one's expenses.", "SPLIT_WILL_BE_TOOLTIP": "This expense will be equally split amongst all employees", "SPLIT_EXPENSE": "Split Expense", "ADD_EXPENSE_CATEGORY": "Add New Expense category", "EXPENSE_CATEGORY": "Expense Category", "RECURRING_EXPENSES": {"WARNING": "Warning: This period overlaps with an existing previous record(s):", "FROM": "From", "TO": "to", "VALUE_OVERWRITTEN": "The value will be overwritten from", "ERROR": "Error: This period overlaps with existing future record(s).", "NOT_SUPPORTED": "This is not supported, please edit the future expenses instead.", "EDIT_FUTURE_VALUE": "This will only edit the future value from", "EXISTING_VALUE": "existing value of", "STARTED_ON": "Started on", "AFFECTED": "will not be affected until", "SET_EXPENSE_VALUE": "This will set the expense value", "ONWARDS": "onwards and the existing value of", "ENDING_ON": "and ending on", "SET_UNTIL": "will be now set until", "REDUCE_START_DATE": "This will reduce the start date and include all the months from", "FOR_EXPENSE_VALUE": "for expense value", "CHANGE_START_DATE": "This will change the start date to"}}, "EMPLOYEES_PAGE": {"HEADER": "Manage Employees", "ADD_EMPLOYEE": "Add Employee", "ACTIVE": "Active", "END_WORK": "End Work", "WORK_ENDED": "Work Ended", "DELETED": "Deleted", "ENABLED": "Enabled", "DISABLED": "Disabled", "RECURRING_EXPENSE": "Employee Recurring Expense", "RECURRING_EXPENSE_EDIT": "Edit the current & all future expenses. There will be no change to any past expense.", "RECURRING_EXPENSE_ADD": "This will add an expense recurring every month.", "RECURRING_EXPENSE_SET": "'{{ name }}' recurring expense set.", "RECURRING_EXPENSE_EDITED": "'{{ name }}' recurring expense edited.", "RECURRING_EXPENSE_DELETED": "'{{ name }}' recurring expense deleted.", "EMPLOYEE_NAME": "Employee", "BACK_TO_WORK": "Back to Work", "SELECT_EMPLOYEE_MSG": "Please select employee from the menu above.", "EDIT_EMPLOYEE": {"SETTINGS_SECTION": "Settings Section", "HEADER": "Manage Employee", "DEVELOPER": "Developer", "DEPARTMENT": "Department", "POSITION": "Position", "EMPLOYEE_DEPARTMENTS": "Employee's Departments:", "EMPLOYEE_PROJECTS": "Employee's Projects:", "EMPLOYEE_CONTACTS": "Employee's Contacts:", "EMPLOYMENT_TYPE": "Employment Type", "ACCOUNT": "Account", "EMPLOYMENT": "Employment", "LOCATION": "Location", "RATES": "Rates", "PROJECTS": "Projects", "CONTACTS": "Contacts", "HIRING": "Hiring", "NETWORKS": "Networks", "EMPLOYEE_LEVEL": "Employee Level", "DISPLAY_BONUS_ANONYMOUSLY": "Display bonus Anonymously", "JOB_SUCCESS": "Job Success", "TOTAL_JOBS": "Total Jobs", "TOTAL_HOURS": "Total Hours", "RATE": "Rate", "VETTED": "Vetted", "HR": "hr", "SETTINGS": "Settings", "GENERAL_SETTINGS": "General Settings", "INTEGRATIONS": "Integrations"}, "ADD_EMPLOYEES": {"STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "ADD_ANOTHER_EMPLOYEE": "Add Another Employee", "FINISHED_ADDING": "I’ve Added All Current Employees", "NEXT": "Next", "PREVIOUS": "Previous"}, "NOT_STARTED": "Not Started", "NOT_STARTED_HELP": "Started work on date is not set for this employee. The employee will not be considered in accounts, split expenses etc."}, "GOALS_PAGE": {"HEADER": "All Objectives", "GOAL": "Goal", "GOALS_EMPTY": "You haven't created any Objectives yet. Click Add New to create one", "ADD_NEW_KEY_RESULT": "Add new key result", "ADD_NEW_OBJECTIVE": "Add new Objective", "EDIT_OBJECTIVE": "Edit Objective", "SESSION": "Session", "GOAL_SETTINGS": "Goals Settings", "NO_DESCRIPTION": "No Description", "PROGRESS": "Progress", "EXPECTED": "Expected", "UPDATES": "Updates", "UPDATE": "Update", "COMMENTS": "Comments", "KEY_RESULTS": "Key Results", "GROUP_BY": "Group By", "DELETE_OBJECTIVE": "Delete Objective", "DELETE_KEY_RESULT": "Delete Key Result", "ARE_YOU_SURE": "Are you sure? This action is irreversible.", "ALL_OBJECTIVES": "All Objectives", "MY_TEAMS_OBJECTIVES": "My Team's Objectives", "MY_ORGANIZATIONS_OBJECTIVES": "'My Organization Objectives", "MY_OBJECTIVES": "My Objectives", "OBJECTIVE_LEVEL": "Objective Level", "TIME_FRAME": "Time Frames", "CREATE_NEW": "+ create new", "GOAL_TEMPLATES": "Goal Templates", "CREATE_NEW_MENU": "Create New", "CREATE_FROM_PRESET": "Create from Preset", "OWNERSHIP": {"EMPLOYEES": "Employees", "TEAMS": "Teams", "EMPLOYEES_AND_TEAMS": "Employees and Teams"}, "SETTINGS": {"ADD_TIME_FRAME_TITLE": "Add Time Frame", "EDIT_TIME_FRAME_TITLE": "Edit Time Frame", "TIME_FRAME_PAGE_TITLE": "Set Time Frame", "PREDEFINED_TIME_FRAMES": "Predefined Time Frames", "ADD_KPI": "Add KPI", "EDIT_KPI": "Edit KPI", "MAX_ENTITIES": "Max Number of entities that can be created", "EMPLOYEE_OBJECTIVES": "Employees can create their own objectives", "WHO_CAN_OWN_OBJECTIVES": "Who can own Objectives?", "WHO_CAN_OWN_KEY_RESULTS": "Who can own Key Results?", "ADD_KPI_TO_KEY_RESULT": "Add KPI to Key Result Type?", "ADD_TASK_TO_KEY_RESULT": "Add Task to Key Result Type?", "GENERAL": "General", "KPI": "KPI", "DELETE_TIME_FRAME_TITLE": "Delete Time Frame", "DELETE_TIME_FRAME_CONFIRMATION": "Are you sure you want to delete Time Frame?", "DELETE_KPI_TITLE": "Delete KPI", "DELETE_KPI_CONFIRMATION": "Are you sure you want to delete KPI?", "ANNUAL": "Annual"}, "MESSAGE": {"NO_KEY_RESULT": "No Key Results to display.", "NO_UPDATES": "No Updates yet.", "NO_ALIGNMENT": "No Alignments yet."}, "LEVELS": {"ORGANIZATION": "Organization", "TEAM": "Team", "EMPLOYEE": "Employee"}, "TIME_FRAME_STATUS": {"ACTIVE": "Active", "INACTIVE": "Inactive"}, "KPI_OPERATOR": {"GREATER_THAN_EQUAL_TO": "Greater than or equal to", "LESSER_THAN_EQUAL_TO": "Lesser than or equal to"}, "KPI_METRIC": {"NUMERICAL": "Numerical", "PERCENTAGE": "Percentage", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>"}, "TOOLTIPS": {"PROGRESS": "Overall progress of this Objective based on its Key result's progress", "DETAILS": "Objective Details", "EDIT": "Edit Objective"}, "FORM": {"LABELS": {"LEVEL": "Level", "OWNER": "Owner", "LEAD": "Lead", "LEAD_OPTIONAL": "Lead (optional)", "DEADLINE": "Deadline", "STATUS": "Status", "KPI_SHOULD_BE": "This KPI should be", "KPI_METRIC": "KPI Metric", "CURRENT_VALUE": "Current Value", "OBJECTIVE": "Objectives", "KEY_RESULT": "Key Results"}, "PLACEHOLDERS": {"NAME": "Objective Name. eg. Improve Website SEO", "DESCRIPTION": "Objective Description.", "LEVEL": "Goal Level", "TIME_FRAME_NAME": "Time frame name eg. 'Q4-2020'", "KPI_DESCRIPTION": "A short description to give some context about the KPI", "KPI_NAME": "KPI Title. eg. Maintain page views - 200/day"}, "ERROR": {"START_DATE_GREATER": "End Date must be greater than Start Date"}}, "BUTTONS": {"ADD_TIME_FRAME": "Add Time Frame"}, "HELPER_TEXT": {"OBJECTIVE_GENERAL": "An <b>Objective</b> is a description of a goal to be achieved in the future.", "OBJECTIVE_TITLE": "<p>Create an ambitious title that best describes your goal.</p><p>The title should not contain any metric</p>eg.<ul><li>Generate more revenue than last year</li><li>Become market leader</li><li>Increase website engagement</li></ul>", "OBJECTIVE_DESCRIPTION": "Write a short description to give some context to your Objective", "OBJECTIVE_LEVEL": "The group to which this Objective belongs to.", "OBJECTIVE_OWNER": "The group/ individual responsible for completing the Objective.", "OBJECTIVE_LEAD": "The person responsible for organizing and planning that is needed to achieve the Objective.", "OBJECTIVE_TIMEFRAME": "Time Frame for which this Objective is created. If a time frame doesn't exist, create one here.", "KPI_GENERAL": "Organizations use <b> Key Performance Indicators (KPIs) </b> to evaluate their success at reaching targets. KPIs can be used as measurable metrics for Key results.", "KPI_NAME": "<p>Name your new KPI</p><p>Examples for good KPI title</p><ul><li># of Followers</li><li>Annual Recurring Revenue</li><li>Customer Lifetime Value</li></ul>", "KPI_DESCRIPTION": "Write a short description to give some context to your KPI", "KPI_METRIC": "KPI measurable unit", "KPI_LEAD": "Person who is responsible for updating the values of this KPI"}}, "KEY_RESULT_PAGE": {"UPDATE_KEY_RESULT": "Update Key Result", "EDIT_KEY_RESULT": "Edit Key Result", "EDIT_KEY_RESULT_PARAMETERS": "Edit Key Result Parameters", "ADD_KEY_RESULT": "Add Key Result", "UPDATE": {"STATUS": {"ON_TRACK": "on track", "NEEDS_ATTENTION": "needs attention", "OFF_TRACK": "off track", "NONE": "none"}}, "WEIGHT": {"DEFAULT": "<PERSON><PERSON><PERSON>", "INCREASE_BY_2X": "Increase 2X", "INCREASE_BY_4X": "Increase 4X", "MESSAGE": "Weights can be used to increase or decrease the importance of a single Key Result when calculating overall Objective Progress %", "OBJECTIVE_PROGRESS": "{{ weight }}% of Objective's Progress"}, "MESSAGE": {"TIME_FRAME_ENDED": "You can't update this key result now. The Time Frame for this key result has ended on {{ date }}", "TIME_FRAME_NOT_STARTED": "You can't update this key result now. The Time Frame for this key result starts on {{ date }}. Then you'll be able to update it"}, "TYPE": {"NUMERICAL": "Numerical", "TRUE_OR_FALSE": "True/False", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "TASK": "Task", "KPI": "KPI"}, "DEADLINE": {"NO_CUSTOM_DEADLINE": "No Custom Deadline", "HARD_DEADLINE": "Hard Deadline", "HARD_AND_SOFT_DEADLINE": "Hard and Soft Deadline"}, "TOOLTIPS": {"PROGRESS": "This Key result's progress contributes to {{weight}}% of the Objective's progress", "DETAILS": "Key Result Details", "EDIT": "Edit Key Result", "WEIGHT": "Edit Weight/Type"}, "FORM": {"LABELS": {"KEY_RESULT_TYPE": "Key Result Type", "INITIAL_VALUE": "Initial value", "TARGET_VALUE": "Target value", "OWNER": "Owner", "LEAD": "Lead (optional)", "DEADLINE": "Deadline", "SOFT_DEADLINE": "Soft Deadline", "HARD_DEADLINE": "Hard Deadline", "UPDATED_VALUE": "Updated Value", "MARK_COMPLETE": "Mark as Complete", "STATUS": "Status", "WEIGHT": "Weight", "TYPE": "Type", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_KPI": "Select KPI", "ASSIGN_AS_OBJECTIVE": "Assign as objective"}, "PLACEHOLDERS": {"NAME": "Key Result Name. eg. Add Metadata to improve SEO", "DESCRIPTION": "A short description to give some context about the Key Result"}}, "HELPER_TEXT": {"KEY_RESULT_GENERAL": "A <b>Key Result</b> is how you plan to measure that you have achieved your objective .", "KEY_RESULT_OWNER": "The person responsible for completing the Key Result.", "KEY_RESULT_LEAD": "The person responsible for organizing and planning that is needed to achieve the Key Result."}}, "CANDIDATES_PAGE": {"HEADER": "Manage Candidates", "ADD_CANDIDATE": "Add Candidates", "APPLIED": "Applied", "HIRED": "<PERSON><PERSON>", "REJECTED": "Rejected", "SHOW_REJECTED": "Show rejected", "DELETED": "Deleted", "ARCHIVED": "Archived", "SELECT_EMPLOYEE_MSG": "Please select employee from the menu above.", "JOBS_CANDIDATES": "Job Candidates", "SOURCE": "Source", "RATING": "Rating", "STATUS": "Status", "EDIT_CANDIDATE": {"HEADER": "Manage Candidate", "DEVELOPER": "Developer", "DEPARTMENT": "Department", "POSITION": "Position", "CANDIDATE_DEPARTMENTS": "Employee's Departments:", "EMPLOYMENT_TYPE": "Employment Type", "CANDIDATES_LEVEL": "Candidate Level", "ACCOUNT": "Account", "EMPLOYMENT": "Employment", "LOCATION": "Location", "RATE": "Rates", "HIRING": "Hiring", "EXPERIENCE": "Experience", "SKILLS": "Skills", "ALL_FEEDBACKS": "All feedbacks", "FEEDBACKS": "Feedbacks", "EDUCATION": "Education", "SCHOOL_NAME": "School name", "DEGREE": "Degree/Diploma", "FIELD": "Field(s)", "COMPLETION_DATE": "Date of completion", "ADDITIONAL_NOTES": "Additional notes", "OCCUPATION": "Occupation", "ORGANIZATION": "Organization name", "DURATION": "Duration", "DESCRIPTION": "Description", "TASKS": "Tasks", "HISTORY": "History", "DOCUMENTS": "Documents", "DOCUMENT_NAME": "Document name", "NAME": "Name", "DOCUMENT": "Document", "FEEDBACK_DESCRIPTION": "Feedback description", "INTERVIEW_FEEDBACK": "Interview: ", "INTERVIEWER": "Interviewer", "FEEDBACK_STATUS": "Status", "LEAVE_FEEDBACK": "Leave a feedback about the ", "STATUS": "Selected status: ", "INTERVIEW": {"INTERVIEW": "Interview", "INTERVIEWS": "Interviews", "INTERVIEWER": "Interviewer: ", "ADD_INTERVIEW": "Add interview", "HIDE_PAST": "<PERSON>de past interviews", "ON": "on", "TO": "To", "FROM": "from", "WITH": "with", "SCHEDULE_INTERVIEW": "Schedule interview", "SCHEDULED_INTERVIEWS": "Scheduled interviews", "CONTINUE": "Continue ", "PAST_DATE": "You have chosen a day in the past", "EDIT_INTERVIEW": "Edit interview", "STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "NEXT": "Next", "PREVIOUS": "Previous", "SAVE": "Save", "CREATE_CRITERIONS": "Create criterions", "CLOSE_CRITERIONS": "Close criterions", "EMAIL_NOTIFICATION": "Email notification", "DELETE_INTERVIEW": "Delete interview", "DELETE_FEEDBACK": "Delete feedback", "DELETE_INTERVIEW_ARE_YOU_SURE": "Are you sure you want to delete this interview?", "DELETE_FEEDBACK_ARE_YOU_SURE": "Are you sure you want to delete this feedback?", "SUMMARY": "Summary", "DETAILS": "Send interview details to", "NOTIFY_CANDIDATE": "Notify candidate", "NOTIFY_INTERVIEWERS": "Notify interviewer(s)", "INTERVIEWERS": "interviewer(s)", "HIRE": "<PERSON>re", "RATING": "Rating", "DESCRIPTION": "Description: ", "POSTPONE": "Postpone", "REJECT": "Reject", "INTERVIEW_TITLE_EXIST": "An interview with such name already exists", "SET_AS_ARCHIVED": "{{ title }} set as archived."}}, "INTERVIEW_INFO_MODAL": {"SCHEDULED": "Scheduled", "HOURS_AGO": " hour(s) ago", "LESS_MINUTE": " less than a minute ago", "MINUTES_AGO": " minutes ago", "DAYS_AGO": " day(s) ago", "DATE": "Date", "TIME": "Time", "CANDIDATE": "Candidate", "INTERVIEWERS": "Interviewers", "LOCATION": "Location", "NOTE": "Note", "OF": "of", "INTERVIEWS_LOWER_CASE": "interviews"}, "ADD_CANDIDATES": {"STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "UPLOAD_CV": "Upload CV", "ADD_ANOTHER_CANDIDATE": "Add Another Candidate", "FINISHED_ADDING": "I’ve Added All Current Candidates", "NEXT": "Next", "PREVIOUS": "Previous"}, "MANAGE_INTERVIEWS": {"CALENDAR": "Calendar", "INTERVIEWS": "Interviews", "MANAGE_INTERVIEWS": "Manage Interviews", "CRITERIONS": "Criterions", "DEFAULT": "Default list", "DATE": "Date", "SORT_BY": "Sort by", "SEARCH_BY_INTERVIEW": "Search by interview's title", "SEARCH_BY_CANDIDATE": "Search by candidate's name", "CANDIDATE_NAME": "Candi<PERSON>'s name", "RATING": "Rating", "RESET_FILTERS": "Reset filters", "TITLE": "Title", "INTERVIEWERS": "Interviewers", "ADD_FEEDBACK": "Add a feedback", "LOCATION": "Location", "NOTES": "Notes", "ACTIONS": "Actions", "EDIT": "Edit", "ARCHIVE": "Archive", "PAST": "Past", "DELETE": "Delete", "UPDATED": "Updated", "CANDIDATE": "Candidate", "FEEDBACK_PROHIBIT": "Adding feedback about future interviews is prohibited", "START_DATE": "Start Date"}, "STATISTIC": {"STATISTICS": "Statistic", "RATING": "Overall rating", "INTERVIEWER_ASSESSMENT": "Rating per interview", "CRITERIONS_RATING": "Criterion's rating per interview", "CANDIDATE_CRITERIONS_RATING": "Average criterion's rating", "NO_DATA": "No data yet ", "SELECT_INTERVIEW": "Select an interview", "SELECT_INTERVIEW_INTERVIEWER": "Select an interview and interviewer", "SELECT_CANDIDATE": "Select a candidate"}, "CRITERIONS": {"CANDIDATE_CRITERIONS": "Candidates criterions", "RATE_CANDIDATE_BY_CRITERIONS": "Rate candidate by criterions", "TECHNOLOGY_STACK": "Technology Stack", "ALREADY_EXISTED": "Already existed", "TOASTR_ALREADY_EXIST": "A criterion with such name already exists", "PERSONAL_QUALITIES": "Personal Qualities", "CHOOSE_CRITERIONS": "Choose criterions for candidate", "TECHNOLOGY_PLACEHOLDER": "If you want to add criteria from the technology stack, you need to create them", "PERSONAL_QUALITIES_PLACEHOLDER": "If you want to add criteria from the personal quality list, you need to create them"}}, "ORGANIZATIONS_PAGE": {"ORGANIZATIONS": "Organizations", "EMPLOYEES": "Employees", "POSITIONS": "Positions", "EDIT_PUBLIC_PAGE": "Edit Public Page", "SELECT_ORGANIZATION": "Please select organizations from the menu above.", "MAIN": "Main", "TAGS_OPTIONS": "Tags & Options", "VARIANTS": "Variants", "DESCRIPTION": "Description", "DEPARTMENTS": "Departments", "VENDORS": "Vend<PERSON>", "VENDOR": "<PERSON><PERSON><PERSON>", "NAME": "Name", "EXPENSE_CATEGORIES": "Expense Categories", "PROJECTS": "Projects", "ACTIVE": "Active", "EMPLOYMENT_TYPE": "Employment Type", "ARCHIVED": "Archived", "LOCATION": "Location", "SETTINGS": "Settings", "AUTOMATION": "Automations", "REGISTER_AS_EMPLOYEE": "Register as Employee", "TEAMS": "Teams", "TEAM_NAME": "{{ name }} Team", "NOT_WORKED": "Not Worked", "ROLES": "Roles", "HELP_CENTER": "Help Center", "DOCUMENTS": "Documents", "DOCUMENTS_NO_DATA_MESSAGE": "You have not created any department.", "EXPENSE_RECURRING": "Recurring Expenses", "RECURRING_EXPENSE": "Organization Recurring Expense", "EMPLOYMENT_TYPES": "Employment Types", "INVITE_CONTACT": "Invite Contact", "EMAIL_INVITE": "<PERSON><PERSON>", "ADD_LEVEL_OF_EMPLOYEE": "Add level of employee", "LEVEL_OF_EMPLOYEE": "Employee Levels", "EMPLOYEE_LEVEL_NO_DATA_MESSAGE": "You have not created any employee level.", "POSITION_NO_DATA_MESSAGE": "You have not created any position.", "PHONE": "Phone", "EMAIL": "Email", "WEBSITE": "Website", "TAGS": "Tags", "DOCUMENT_URL": "Document URL", "UPDATED": "Updated", "LEVEL_NAME": "Level name", "EXPENSE_NAME": "Expense name", "EMPLOYMENT_TYPE_NAME": "Employment type name", "EMPLOYMENT_TYPE_NO_DATA_MESSAGE": "You have not created any employment type.", "VENDORS_NO_DATA_MESSAGE": "You have not created any vendor.", "EXPENSE_NO_DATA_MESSAGE": "You have not created any recurring expense.", "CONTACTS": "Contacts", "LEVEL": "Level", "ORGANIZATION": "ORGANIZATION", "HOURS_WORKED": "hours worked", "CLIENTS": "Clients", "PROFILE": "Profile", "PORTFOLIO": "Portfolio", "BROWSE": "Browse", "SEARCH": "Search", "EDIT": {"SETTINGS_SECTION": "Settings Section", "ALL": "All", "ACCOUNTING": "Accounting", "HEADER": "Manage", "CLIENT": "Client", "CONTACT": "Contact", "NEW_CLIENT": "Add new client", "NAME": "Name", "PRIMARY_EMAIL": "Primary Email", "PHONE": "Primary Phone", "COUNTRY": "Country", "CITY": "City", "STREET": "Street", "PROJECTS": "Projects", "FAX": "Fax", "FISCAL_INFORMATION": "Fiscal Information", "WEBSITE": "Website", "SECOND_ADDRESS": "Street 2", "IMAGE_URL": "Image URL", "POSTCODE": "Postcode", "DEPARTMENT_NAME": "Department name", "POSITION_NAME": "Position name", "NEW_PROJECT": "Add new project", "START_DATE": "Start Date", "END_DATE": "End Date", "BILLING": "Billing", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "OWNER": "Owner", "TEAMS": "Teams", "ADD_NEW_CONTACT": "Add New Contact", "EDIT_CONTACT": "Edit Contact", "GENERAL_SETTINGS": "General Settings", "DESIGN": "Design", "BONUS": "Bonus", "INVITE": "<PERSON><PERSON><PERSON>", "CLICK_EMPLOYEE": "Click to edit employee", "EDIT_PROJECT": "Edit Project", "REGIONS": "Regions", "ROLES_PERMISSIONS": "Roles & Permissions", "DATE_LIMIT": "Date Limit", "USER_ORGANIZATIONS": "{{ name }}'s List of Organizations", "ADDED_TO_ORGANIZATION": " the organization", "USER_WAS_DELETED": "'{{ name }}' was removed", "USER_WAS_REMOVED": "'{{ name }}' was removed from organization.", "EMPLOYEE_POSITION": "Employee Position", "PROJECT_URL": "Project Url", "VISIBILITY": "Visibility", "MEMBERS": "Employee/Teams", "INTEGRATIONS": "Integrations", "SETTINGS": {"TIMER_SETTINGS": "Timer <PERSON>s", "AGENT_SETTINGS": "Agent <PERSON>s", "ALLOW_MODIFY_TIME": "Allow Modify Time", "ALLOW_MODIFY_TIME_INFO": "Allow employee to modify manual time.", "ALLOW_DELETE_TIME": "Allow Delete Time", "ALLOW_DELETE_TIME_INFO": "Allow employee to delete time.", "ALLOW_AGENT_APP_EXIT": "Allow Agent App Exit", "ALLOW_AGENT_APP_EXIT_INFO": "Allow employee to exit from the Agent application. When disabled, the exit option will be blocked in the Agent app.", "ALLOW_LOGOUT_FROM_AGENT_APP": "Allow <PERSON> from Agent App", "ALLOW_LOGOUT_FROM_AGENT_APP_INFO": "Allow employee to logout from the Agent application. When disabled, the logout option will be blocked in the Agent app.", "TRACK_KEYBOARD_MOUSE_ACTIVITY": "Track Keyboard & Mouse Activity", "TRACK_KEYBOARD_MOUSE_ACTIVITY_INFO": "Enable full keyboard and mouse activity tracking, including keystrokes and exact mouse movements in all desktop apps and agent.", "TRACK_ALL_DISPLAYS": "Track All Displays", "TRACK_ALL_DISPLAYS_INFO": "Enable tracking on all displays or limit to primary display only.", "ALLOW_MANUAL_TIME": "Allow Manual Time", "ALLOW_MANUAL_TIME_INFO": "Allow employee to add manual time.", "REQUIRE_REASON": "Require Reason", "REQUIRE_REASON_INFO": "Reason of the add manual time or edit time logs.", "REQUIRE_DESCRIPTION": "Require Description", "REQUIRE_DESCRIPTION_INFO": "Description of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_PROJECT": "Require Project", "REQUIRE_PROJECT_INFO": "Project of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_TASK": "Require Task", "REQUIRE_TASK_INFO": "Task of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_CLIENT": "Require Client", "REQUIRE_CLIENT_INFO": "Client of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "ALLOW_TO_SET_DEFAULT_ORGANIZATION": "Allow user to set default organization", "INACTIVITY_TIME_LIMIT": "Inactivity Limit", "INACTIVITY_TIME_LIMIT_INFO": "Inactivity tracking timeout (i.e., how many minutes of inactivity is allowed)", "ACTIVITY_PROOF_DURATION": "Activity proof duration", "ACTIVITY_PROOF_DURATION_INFO": "Duration of activity proof countdown dialog, it represent an amount of minutes given to prove activity", "ENABLE_DETECTION_INACTIVITY": "Enable detection of employee inactivity", "DELETE_IDLE_TIME": "Enable deletion of idle time", "DELETE_IDLE_TIME_INFO": "Enable - the system will delete idle time. Disable - the system will not delete idle time", "ALLOW_SCREEN_CAPTURE": "Allow Screen Capture", "ALLOW_SCREEN_CAPTURE_INFO": "Enable - the timer will take screenshot. Disable - the timer will run without to take screenshot for all organization members", "SCREENSHOT_FREQUENCY": "Screenshot Frequency", "SCREENSHOT_FREQUENCY_INFO": "Specify the update frequency or time intervals synchronization.", "RANDOM_SCREENSHOT": "Random Screenshot", "RANDOM_SCREENSHOT_INFO": "Enable - Taking screenshots at random intervals. Disable - Taking screenshots at regular intervals", "TRACK_ON_SLEEP": "Track On Sleep", "TRACK_ON_SLEEP_INFO": "Enable - Tracking when the device goes to sleep. Disable - Timer Stop tracking time when device goes sleep.", "ENFORCED": "Enforced", "ENFORCED_INFO": "Enabled - Ensures that track on sleep, random screenshot and screenshot frequency settings, specify by the admin are mandatory and cannot be overridden or ignored by users.", "TASK_SETTING": "Task Settings", "TASK_PRIVACY": "Task Privacy", "MULTIPLE_ASSIGNEE": "Multiple Assignees", "MANUAL_TIME": "Manual Time", "GROUP_ESTIMATION": "Group Estimation", "ESTIMATION_IN_HOUR": "Estimation in hour", "ESTIMATION_IN_STORY_POINT": "Estimation in story point", "PROOF_OF_COMPLETION": "Proof of Completion", "PROOF_OF_COMPLETION_TYPE": "Proof of Completion Type", "PROOF_OF_COMPLETION_TYPE_DROPDOWN": {"NONE": "None", "PRIVATE": "Private", "PUBLIC": "Public"}, "LINKED_ISSUE": "Linked Issue", "COMMENT": "Comment", "HISTORY": "History", "ACCEPTANCE_CRITERIA": "Acceptance Criteria", "DRAFT_ISSUE": "Draft Issues", "NOTIFY_TASK_LEFT": "Notify Task Left", "NOTIFY_TASK_LEFT_PERIOD": "Notify Task Period (in Days)", "AUTO_CLOSE_ISSUE": "Auto Close Issue", "AUTO_CLOSE_ISSUE_PERIOD": "Auto Close Issue Period (in Days)", "AUTO_ARCHIVE_ISSUE": "Auto Archive Issue", "AUTO_ARCHIVE_ISSUE_PERIOD": "Auto Archive Issue Period (in Days)", "AUTO_STATUS": "Auto Status", "TOOLTIP": {"ENABLE_DETECTION_INACTIVITY_INFO": "Enabled - the system will detect inactivity and display a warning. Disabled - no warning will be displayed when an employee is inactive.", "ALLOW_MANUAL_TIME_INFO": "Indicates whether manual time entry is allowed for time tracking.", "ALLOW_MODIFY_TIME_INFO": "Indicates whether modification of time entries is allowed for time tracking.", "ALLOW_DELETE_TIME_INFO": "Indicates whether deletion of time entries is allowed for time tracking.", "TASK_PRIVACY_ENABLED_INFO": "Indicates whether tasks privacy features are enabled.", "TASK_MULTIPLE_ASSIGNEE_ENABLED_INFO": "Indicates whether tasks allow multiple assignees.", "TASK_MANUAL_TIME_ENABLED_INFO": "Indicates whether manual time tracking is enabled for tasks.", "TASK_GROUP_ESTIMATION_ENABLED_INFO": "Indicates whether group estimation is enabled for tasks.", "TASK_ESTIMATION_IN_HOUR_ENABLED_INFO": "Indicates whether task estimation in hours is enabled.", "TASK_ESTIMATION_IN_STORY_POINT_ENABLED_INFO": "Indicates whether task estimation in story points is enabled.", "TASK_PROOF_OF_COMPLETION_ENABLED_INFO": "Indicates whether proof of completion is enabled for tasks.", "TASK_LINKED_ISSUE_ENABLED_INFO": "Indicates whether the linking of tasks is enabled.", "TASK_COMMENTS_ENABLED_INFO": "Indicates whether comments on tasks are enabled.", "TASK_HISTORY_ENABLED_INFO": "Indicates whether the tracking of task history is enabled.", "TASK_ACCEPTANCE_CRITERIA_ENABLED_INFO": "Indicates whether the use of acceptance criteria for tasks is enabled.", "TASK_DRAFT_ISSUE_ENABLED_INFO": "Indicates whether the use of drafts for tasks is enabled.", "TASK_AUTO_STATUS_ENABLED_INFO": "Indicates whether automatic status updates are enabled for tasks.", "TASK_NOTIFY_LEFT_ENABLED_INFO": "Indicates whether notifications about tasks approaching their due date are enabled.", "TASK_AUTO_CLOSE_ENABLED_INFO": "Indicates whether tasks may automatically close after a specified period.", "TASK_AUTO_ARCHIVE_ENABLED_INFO": "Indicates whether tasks may automatically be archived after a specified period.", "STANDARD_WORK_HOURS_PER_DAY": "Select the default number of work hours per day. This value will be used in time-tracking reports to compare how many hours an employee worked relative to this standard."}}, "TEAMS_PAGE": {"MANAGERS": "Managers", "MEMBERS": "Members"}}, "PERMISSIONS": {"ADMIN_DASHBOARD_VIEW": "View Admin Dashboard", "TEAM_DASHBOARD": "View Team Dashboard", "PROJECT_MANAGEMENT_DASHBOARD": "View Project Management Dashboard", "TIME_TRACKING_DASHBOARD": "View Time Tracking Dashboard", "ACCOUNTING_DASHBOARD": "View Accounting Dashboard", "HUMAN_RESOURCE_DASHBOARD": "View Human Resources Dashboard", "ORG_PAYMENT_VIEW": "View Payments", "ORG_PAYMENT_ADD_EDIT": "Create/Edit/Delete Payments", "ORG_EXPENSES_VIEW": "View All Expenses", "ORG_EXPENSES_EDIT": "Create/Edit/Delete Expenses", "EMPLOYEE_EXPENSES_VIEW": "View All Employee Expenses", "EMPLOYEE_EXPENSES_EDIT": "Create/Edit/Delete Employee Expenses", "ORG_INCOMES_EDIT": "Create/Edit/Delete Incomes", "ORG_INCOMES_VIEW": "View All Incomes", "ORG_PROPOSALS_EDIT": "Create/Edit/Delete Proposals Register", "ORG_PROPOSALS_VIEW": "View Proposals Page", "ORG_PROPOSAL_TEMPLATES_VIEW": "View Proposal Templates Page", "ORG_PROPOSAL_TEMPLATES_EDIT": "Create/Edit/Delete Proposal Templates", "ORG_EMPLOYEES_ADD": "Create Organization Employees", "ORG_EMPLOYEES_VIEW": "View Organization Employees", "ORG_EMPLOYEES_EDIT": "Edit Organization Employees", "ORG_EMPLOYEES_DELETE": "Delete Organization Employees", "ORG_CANDIDATES_VIEW": "View Organization Candidates", "ORG_CANDIDATES_EDIT": "Create/Edit/Delete Organization Candidates", "ORG_USERS_VIEW": "View Organization Users", "ORG_USERS_EDIT": "Create/Edit/Delete Organization Users", "ORG_INVITE_VIEW": "View Organization Invites", "ORG_INVITE_EDIT": "Create/Resend/Delete Invites", "ORG_CANDIDATES_DOCUMENTS_VIEW": "View All Candidates Documents", "ORG_CANDIDATES_TASK_EDIT": "Create/Edit Task", "ORG_CANDIDATES_INTERVIEW_EDIT": "Create/Edit Interview", "ORG_CANDIDATES_INTERVIEW_VIEW": "View Interview", "ORG_INVENTORY_PRODUCT_EDIT": "Management Product", "ORG_TAGS_ADD": "Create Tags", "ORG_TAGS_VIEW": "View Tags", "ORG_TAGS_EDIT": "Edit Tags", "ORG_TAGS_DELETE": "Delete Tags", "ORG_TAG_TYPES_ADD": "Add tag type", "ORG_TAG_TYPES_VIEW": "View tag types", "ORG_TAG_TYPES_EDIT": "Edit tag type", "ORG_TAG_TYPES_DELETE": "Delete tag type", "ORG_CANDIDATES_FEEDBACK_EDIT": "Create/Edit/Delete Candidate Feedback", "ALL_ORG_VIEW": "View All Organizations", "ALL_ORG_EDIT": "Create/Edit/Delete All Organizations", "TIME_OFF_POLICY_ADD": "Add Time Off Policy", "TIME_OFF_POLICY_VIEW": "View Time Off Policy", "TIME_OFF_POLICY_EDIT": "Edit Time Off Policy", "TIME_OFF_POLICY_DELETE": "Delete Time Off Policy", "SELECT_EMPLOYEE": "Select Employee", "CHANGE_SELECTED_EMPLOYEE": "Change Selected Employee", "CHANGE_SELECTED_CANDIDATE": "Change Selected Candidate", "CHANGE_SELECTED_ORGANIZATION": "Change Selected Organization", "CHANGE_ROLES_PERMISSIONS": "Change Roles & Permissions", "ACCESS_PRIVATE_PROJECTS": "Access Private Projects", "TIMESHEET_EDIT_TIME": "Edit Time in Timesheet", "INVOICES_VIEW": "View Invoices", "INVOICES_EDIT": "Edit Invoices Add", "ESTIMATES_VIEW": "View Estimates", "ESTIMATES_EDIT": "Edit Estimates Add", "EDIT_SALES_PIPELINES": "Edit Sales Pipelines", "VIEW_SALES_PIPELINES": "View Sales Pipelines", "APPROVALS_POLICY_EDIT": "Edit Approvals Policy", "APPROVALS_POLICY_VIEW": "View Approvals Policy", "REQUEST_APPROVAL_EDIT": "Edit Approval Request", "REQUEST_APPROVAL_VIEW": "View Approval Request", "ORG_CANDIDATES_INTERVIEWERS_EDIT": "Create/Edit Interviewers", "ORG_CANDIDATES_INTERVIEWERS_VIEW": "View Interviewers", "VIEW_ALL_EMAILS": "View All Emails", "VIEW_ALL_EMAIL_TEMPLATES": "View All Emails Templates", "ORG_HELP_CENTER_EDIT": "Edit Organization Help Center", "PUBLIC_PAGE_EDIT": "Edit Organization Public Page", "CAN_APPROVE_TIMESHEET": "Approve Timesheet", "EVENT_TYPES_VIEW": "View Event Types", "TIME_OFF_ADD": "Add Time Off", "TIME_OFF_VIEW": "View Time Off", "TIME_OFF_EDIT": "Edit Time Off", "TIME_OFF_DELETE": "Delete Time Off", "ORG_INVENTORY_VIEW": "View Organization Inventory", "INVENTORY_GALLERY_VIEW": "View Inventory Gallery", "INVENTORY_GALLERY_EDIT": "Edit Inventory Gallery", "MEDIA_GALLERY_ADD": "Add media gallery", "MEDIA_GALLERY_VIEW": "View media gallery", "MEDIA_GALLERY_EDIT": "Edit media gallery", "MEDIA_GALLERY_DELETE": "Delete media gallery", "EQUIPMENT_SHARING_POLICY_ADD": "Add Equipment Sharing Policy", "EQUIPMENT_SHARING_POLICY_VIEW": "View Equipment Sharing Policy", "EQUIPMENT_SHARING_POLICY_EDIT": "Edit Equipment Sharing Policy", "EQUIPMENT_SHARING_POLICY_DELETE": "Delete Equipment Sharing Policy", "ORG_EQUIPMENT_VIEW": "View Organization Equipment", "ORG_EQUIPMENT_EDIT": "Edit Organization Equipment", "ORG_EQUIPMENT_SHARING_VIEW": "View Organization Equipment Sharing", "ORG_EQUIPMENT_SHARING_EDIT": "Edit Organization Equipment Sharing", "EQUIPMENT_MAKE_REQUEST": "Request Make Equipment Make", "EQUIPMENT_APPROVE_REQUEST": "Request Approve Equipment", "ORG_PRODUCT_TYPES_VIEW": "View Organization Product Types", "ORG_PRODUCT_TYPES_EDIT": "Edit Organization Product Types", "ORG_PRODUCT_CATEGORIES_VIEW": "View Organization Product Categories", "ORG_PRODUCT_CATEGORIES_EDIT": "Edit Organization Product Categories", "VIEW_ALL_ACCOUNTING_TEMPLATES": "View All Accounting Templates", "GROUPS": {"GENERAL": "General", "ADMINISTRATION": "Administration"}, "ONLY_ADMIN": "These permissions are read-only and enabled only for admin", "INSUFFICIENT": "You do not have sufficient permissions. The following permissions are missing:", "ORG_SPRINT_EDIT": "Edit sprint", "ORG_SPRINT_VIEW": "View sprint", "ORG_SPRINT_DELETE": "Delete sprints", "ORG_SPRINT_ADD": "Add sprint", "ORG_PROJECT_ADD": "Create Projects", "ORG_PROJECT_VIEW": "View Projects", "ORG_PROJECT_EDIT": "Edit Projects", "ORG_PROJECT_DELETE": "Delete Projects", "ORG_CONTACT_EDIT": "Create/Edit Contacts", "ORG_CONTACT_VIEW": "View Contacts", "DAILY_PLAN_CREATE": "Create Daily Plans", "DAILY_PLAN_READ": "View Daily Plans", "DAILY_PLAN_UPDATE": "Update Daily Plans", "DAILY_PLAN_DELETE": "Delete Daily Plans", "PROJECT_MODULE_CREATE": "Create Project Modules", "PROJECT_MODULE_READ": "View Project Modules", "PROJECT_MODULE_UPDATE": "Update Project Modules", "PROJECT_MODULE_DELETE": "Delete Project Modules", "DASHBOARD_CREATE": "Create Dashboard", "DASHBOARD_READ": "View Dashboard", "DASHBOARD_UPDATE": "Update Dashboard", "DASHBOARD_DELETE": "Delete Dashboard", "ORG_TEAM_ADD": "Add Teams", "ORG_TEAM_VIEW": "View Teams", "ORG_TEAM_EDIT_ACTIVE_TASK": "Edit Active Tasks", "ORG_TEAM_EDIT": "Edit Teams", "ORG_TEAM_DELETE": "Delete Teams", "ORG_TEAM_REMOVE_ACCOUNT_AS_MEMBER": "Re<PERSON><PERSON> Account As Team Member", "ORG_TEAM_JOIN_REQUEST_VIEW": "View Teams Join Requests", "ORG_TEAM_JOIN_REQUEST_EDIT": "Delete Teams Join Requests", "ORG_CONTRACT_EDIT": "Create/Edit Contracts", "TIME_TRACKER": "Access Time Tracker", "TENANT_ADD_EXISTING_USER": "Tenant Add User To Organization", "INTEGRATION_ADD": "Add Integrations", "INTEGRATION_VIEW": "View Integrations", "INTEGRATION_EDIT": "Edit Integrations", "INTEGRATION_DELETE": "Delete Integrations", "FILE_STORAGE_VIEW": "View File Storage", "PAYMENT_GATEWAY_VIEW": "View Payment Gateway", "SMS_GATEWAY_VIEW": "View SMS Gateway", "CUSTOM_SMTP_VIEW": "View Custom SMTP", "IMPORT_EXPORT_VIEW": "View Import/Export", "ORG_JOB_APPLY": "Apply Jobs", "ORG_JOB_EDIT": "Edit Jobs", "ORG_JOB_SEARCH": "View Jobs", "ORG_JOB_EMPLOYEE_VIEW": "View Job Employees", "ORG_JOB_MATCHING_VIEW": "View Job Matching", "ACCESS_DELETE_ACCOUNT": "Access Delete Account", "ACCESS_DELETE_ALL_DATA": "Access Delete All Data", "TENANT_SETTING": "Create/Edit/Delete tenant settings", "ALLOW_DELETE_TIME": "Allow Delete Time", "ALLOW_MODIFY_TIME": "Allow Modify Time", "ALLOW_MANUAL_TIME": "Allow Manual Time", "DELETE_SCREENSHOTS": "Allow Delete Screenshot", "ORG_TASK_ADD": "Create Tasks", "ORG_TASK_VIEW": "View Tasks", "ORG_TASK_EDIT": "Edit Tasks", "ORG_TASK_DELETE": "Delete Tasks", "ORG_TASK_SETTING": "Task Settings", "IMPORT_ADD": "Ability to import your data", "EXPORT_ADD": "Ability to export your data", "ORG_MEMBER_LAST_LOG_VIEW": "View Last Log", "API_CALL_LOG_READ": "Read API Call Log", "API_CALL_LOG_DELETE": "Delete API Call Log", "TENANT_API_KEY_CREATE": "Create API Key", "TENANT_API_KEY_VIEW": "View API Key", "TENANT_API_KEY_DELETE": "Delete API Key", "EMPLOYEE_AVAILABILITY_CREATE": "Create Employee Availability", "EMPLOYEE_AVAILABILITY_READ": "View Employee Availability", "EMPLOYEE_AVAILABILITY_UPDATE": "Update Employee Availability", "EMPLOYEE_AVAILABILITY_DELETE": "Delete Employee Availability"}, "BILLING": "Billing", "BUDGET": "Budget", "OPEN_SOURCE": "Open-Source", "ORGANIZATION_ADD": "Add Organization", "IMAGE": "Image", "SPRINTS": "Sprints", "INTEGRATIONS": "Integrations", "NO_IMAGE": "Image no available"}, "CONTACTS_PAGE": {"VISITORS": "Visitors", "LEADS": "Leads", "CUSTOMERS": "Customers", "CLIENTS": "Clients", "CITY": "City", "STREET": "Street", "COUNTRY": "Country", "PROJECTS": "Projects", "EMAIL": "Primary Email", "PHONE": "Primary Phone", "CONTACT_TYPE": "Contact type", "MAIN": "Main", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "MEMBERS": "Members", "BUDGET": "Budget"}, "PUBLIC_PAGE": {"LANGUAGES": "Languages", "AWARDS": "Awards", "COMPANY_SKILLS": "Company Skills", "SKILLS": "Skills", "PROFILE": "Profile", "PORTFOLIO": "Portfolio", "OVERVIEW": "Overview", "DESCRIPTION": "Description", "TOTAL_BONUSES_PAID": "Total Bonuses Paid", "COMPANY_PROFILE": "Company Profile", "TOTAL_CLIENTS": "Total Clients", "YEAR_FOUNDED": "Year Founded", "COMPANY_SIZE": "Company Size", "CLIENT_FOCUS": "Client Focus", "MONTHLY_INCOME": "Monthly Income", "TOTAL_INCOME": "Total Income", "TOTAL_PROJECTS": "Total Projects", "MINIMUM_PROJECT_SIZE": "Minimum Project Size", "EMPLOYEES": "Employees", "PROFITS": "Profits", "RATE": "Rate", "ACTIVE": "Active", "STARTED_WORK_ON": "Started work on", "PAY_PERIOD": "Pay period", "AVERAGE_BONUS": "Average Bonus", "AVERAGE_EXPENSES": "Average Expenses", "AVERAGE_INCOME": "Average Income", "EMPLOYEE_UPDATED": "Employee has been updated.", "IMAGE_UPDATED": "The image has been updated.", "FAIL_TO_UPDATE_IMAGE": "Update failed.", "ANONYMOUS": "Anonymous"}, "PROPOSALS_PAGE": {"HEADER": "Proposals Management Page", "STATISTICS": "Statistics", "ACCEPTED_PROPOSALS": "Accepted Proposals", "TOTAL_PROPOSALS": "Total Proposals", "SUCCESS_RATE": "Success Rate", "PROPOSALS": "Proposals", "REGISTER": {"REGISTER_PROPOSALS": "Register Proposal", "AUTHOR": "Author", "TEMPLATE": "Template", "JOB_POST_URL": "Job Post URL", "PICK_A_DATE": "Pick a Date", "PROPOSAL_DATE": "Proposal Date", "JOB_POST_CONTENT": "Job Post Content", "UPLOAD": "Upload", "PROPOSALS_CONTENT": "Proposal Content", "REGISTER_PROPOSALS_BUTTON": "Register Proposal"}, "PROPOSAL_DETAILS": {"PROPOSAL_DETAILS": "Proposal Details", "AUTHOR": "Author", "JOB_POST_URL": "Job Post URL", "PROPOSAL_SENT_ON": "Proposal Sent On", "STATUS": "Status", "JOB_POST_CONTENT": "Job Post Content", "PROPOSAL_CONTENT": "Proposal Content", "VIEW_JOB_POST": "View Job Post"}, "EDIT_PROPOSAL": {"EDIT_PROPOSAL": "Edit Proposal", "AUTHOR": "Author", "JOB_POST_URL": "Job Post URL", "PROPOSAL_SENT_ON": "Proposal Sent On", "JOB_POST_CONTENT": "Job Post Content", "PROPOSAL_CONTENT": "Proposal Content", "EDIT_PROPOSAL_BUTTON": "Edit Proposal", "PLACEHOLDER": {"JOB_POST_URL": "Job Post URL"}}}, "APPROVAL_REQUEST_PAGE": {"APPROVAL_REQUEST_NAME": "Name", "APPROVAL_REQUEST_TYPE": "Type", "APPROVAL_REQUEST_MIN_COUNT": "Min Count", "APPROVAL_REQUEST_APPROVAL_POLICY": "Approval Policy", "APPROVAL_REQUEST_STATUS": "Status", "APPROVAL_REQUEST_ACTIONS": "Actions", "CREATED_BY": "Created By", "APPROVE": "Approve", "REFUSE": "Refuse", "HEADER": "Approval Request", "EMPLOYEES": "Employees", "TEAMS": "Teams", "APPROVAL_POLICY": "Approval Policy", "CHOOSE_POLICIES": "Choose policies/s", "EDIT_APPROVAL_REQUEST": "Edit Request Approval", "ADD_APPROVAL_REQUEST": "Add Request Approval", "APPROVAL_REQUEST_CREATED": "Request Approval '{{ name }}' was added", "APPROVAL_REQUEST_UPDATED": "Request Approval '{{ name }}' was changed", "APPROVAL_REQUEST_DELETED": "Request Approval '{{ name }}' was removed", "APPROVAL_SUCCESS": "Approval '{{ name }}' was approved", "REFUSE_SUCCESS": "Approval '{{ name }}' was refused", "APPROVED": "Approved", "REFUSED": "Refused", "REQUESTED": "Requested", "ACTION": "Action", "CREATED_AT": "Created At"}, "APPROVAL_POLICY_PAGE": {"EDIT_APPROVAL_POLICY": "Edit Approval Policy", "ADD_APPROVAL_POLICY": "Add Approval Policy", "HEADER": "Approval Policy", "APPROVAL_POLICY_NAME": "Name", "APPROVAL_POLICY_TYPE": "Type", "APPROVAL_POLICY_DESCRIPTION": "Description", "BUSINESS_TRIP": "Business Trip", "EQUIPMENT_SHARING": "Equipment Sharing", "TIME_OFF": "Time Off"}, "TIME_OFF_PAGE": {"HEADER": "Time Off", "REQUEST": "Request", "REQUEST_TIME_OFF": "Request Time Off", "EDIT": "Edit", "ADD_HOLIDAYS": "Add Holidays", "DISPLAY_HOLIDAYS": "Display Holidays", "HOLIDAY_NAME": "Holiday name", "SELECT_EMPLOYEES": "Select Employees", "SETTINGS": "Settings", "EDI": "Edit Time Off record", "SELECT_HOLIDAY_NAME": "Select Holiday name", "ADD_OR_REMOVE_EMPLOYEES": "Add or Remove Employees", "SELECT_TIME_OFF_POLICY": "Select Time-off Policy", "ADD_A_DESCRIPTION": "Add a description", "DESCRIPTION": "Description", "START_DATE": "Start Date", "END_DATE": "End Date", "REQUEST_DATE": "Request Date", "STATUS": "Status", "TIME_OFF_REQUEST": "Time off request", "VIEW_REQUEST_DOCUMENT": "View Request Document", "MULTIPLE_EMPLOYEES": "Multiple employees", "UPLOAD_REQUEST_DOCUMENT": "Upload Request Document", "STATUSES": {"REQUESTED": "Requested", "APPROVED": "Approved", "DENIED": "Denied", "ALL": "All"}, "ACTIONS": {"EDIT": "Edit Time Off record", "APPROVE_DAYS_OFF_REQUEST": "Approve Time Off Request", "DENY_DAYS_OFF_REQUEST": "Deny Time Off Request", "DELETE_DAYS_OFF_REQUEST": "Delete Time Off Request"}, "POLICY": {"HEADER": "Time Off Policy", "POLICY": "Policy", "ADD_POLICY": "Add Policy", "EDIT_POLICY": "Edit Policy", "NAME": "Name", "REQUIRES_APPROVAL": "Requires Approval", "PAID": "Paid", "NAME_IS_REQUIRED": "Policy name is required!"}, "NOTIFICATIONS": {"NO_CHANGES": "No changes", "STATUS_SET_APPROVED": "You successfully set the time off request status to approved.", "ERR_SET_STATUS": "Unable to set time off request status.", "APPROVED_NO_CHANGES": "The time off request status is already set to approved", "RECORD_CREATED": "Time off record was saved", "REQUEST_DENIED": "You successfully set the time off request status to denied", "DENIED_NO_CHANGES": "The time off request status is already set to denied", "REQUEST_DELETED": "Time off request was removed", "ERR_LOAD_RECORDS": "Unable to load time off records", "ERR_DELETE_REQUEST": "Unable to delete Time off request", "ERR_CREATE_RECORD": "Unable to create Time off record", "REQUEST_UPDATED": "Time Off request successfully updated", "ERR_UPDATE_RECORD": "Unable to update Time off record"}}, "TAGS_PAGE": {"HEADER": "Tags", "ADD_TAGS": "Add Tags", "EDIT_TAGS": "Edit Tags", "TAGS_NAME": "Name", "TAGS_DESCRIPTION": "Description", "TAGS_COLOR": "Color", "TAGS_ADD_TAG": "Tag '{{ name }}' was added", "TAGS_EDIT_TAG": "Tag '{{ name }}' was changed", "TAGS_DELETE_TAG": "Tag '{{ name }}' was removed", "TAGS_SELECT_NAME": "Tag name", "TAGS_SELECT_COLOR": "Tag color", "TAGS_SELECT_DESCRIPTION": "Tag description", "ADD_NEW_TAG": "Add new Tag", "TENANT_LEVEL": "Tenant level", "TAGS_TYPE": "Tags type", "TAGS_SELECT_TYPE": "Select tag type"}, "SKILLS_PAGE": {"HEADER": "Skills"}, "LANGUAGE_PAGE": {"HEADER": "Languages", "ADD_NEW_LANGUAGE": "Add new Language"}, "LANGUAGE_LEVELS": {"CONVERSATIONAL": "Conversational", "NATIVE": "Native", "FLUENT": "Fluent"}, "EQUIPMENT_PAGE": {"HEADER": "Equipment", "ADD_EQUIPMENT": "Add Equipment", "EDIT_EQUIPMENT": "Edit Equipment", "EQUIPMENT_NAME": "Name", "EQUIPMENT_TYPE": "Type", "EQUIPMENT_SN": "SN", "EQUIPMENT_MANUFACTURED_YEAR": "Manufactured year", "EQUIPMENT_INITIAL_COST": "Initial cost", "EQUIPMENT_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "EQUIPMENT_MAX_SHARE_PERIOD": "Max share period", "EQUIPMENT_AUTO_APPROVE": "Auto approve", "EQUIPMENT_EDITED": "Equipment edited", "EQUIPMENT_DELETED": "Equipment '{{ name }}' was removed", "EQUIPMENT_ADDED": "Equipment added", "EQUIPMENT_SAVED": "Equipment '{{ name }}' was saved", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>"}, "EQUIPMENT_SHARING_PAGE": {"HEADER": "Equipment Sharing", "ADD_EQUIPMENT_REQUEST": "Add Equipment Request", "EDIT_EQUIPMENT_REQUEST": "Edit Equipment Request", "DELETE_EQUIPMENT_REQUEST": "Delete Equipment Request", "REQUEST": "Request", "EQUIPMENT_NAME": "Equipment name", "EQUIPMENT_SHARING_POLICY": "Equipment Sharing Policy", "SHARE_REQUEST_DATE": "Share request date", "SHARE_START_DATE": "Share start date", "SHARE_END_DATE": "Share end date", "CREATED_BY": "Created By", "STATUS": "Status", "REQUEST_SAVED": "Request was saved", "REQUEST_DELETED": "Request was removed", "MESSAGES": {"BEFORE_REQUEST_DAY_ERR": "Date must be after request day.", "EXCEED_PERIOD_ERR": "The maximum allowed days for this item are ", "BEFORE_START_DATE_ERR": "End date cannot be before start date. ", "ITEM_RETURNED_BEFORE_ERR": "Item should be returned before "}, "ACTIONS": "Actions", "APPROVE": "Approve", "REFUSE": "Refuse", "APPROVAL_SUCCESS": "Request Approval Success", "REFUSE_SUCCESS": "Request Refuse Success", "APPROVED": "Approved", "REFUSED": "Refused", "REQUESTED": "Requested", "ACTION": "Action"}, "EQUIPMENT_SHARING_POLICY_PAGE": {"HEADER": "Equipment Sharing Policy", "ADD_EQUIPMENT_SHARING_POLICY": "Add Equipment Sharing Policy", "EDIT_EQUIPMENT_SHARING_POLICY": "Edit Equipment Sharing Policy", "DELETE_EQUIPMENT_SHARING_POLICY": "Delete Equipment Sharing Policy", "REQUEST": "Request", "EQUIPMENT_SHARING_POLICY_NAME": "Equipment Sharing Policy name", "EQUIPMENT_SHARING_POLICY_ORG": "Equipment Sharing Policy organization", "EQUIPMENT_SHARING_POLICY_DESCRIPTION": "Equipment Sharing Policy description", "REQUEST_SAVED": "Policy saved", "REQUEST_DELETED": "Policy deleted", "ACTIONS": "Actions", "MESSAGES": {"EQUIPMENT_REQUEST_SAVED": "Request '{{ name }}' was changed", "EQUIPMENT_REQUEST_DELETED": "Request '{{ name }}' was removed"}}, "INVENTORY_PAGE": {"HEADER": "Inventory", "ADD_INVENTORY_ITEM": "Add inventory item", "EDIT_INVENTORY_ITEM": "Edit inventory item", "INVENTORY_ITEM_DELETED": "Inventory '{{ name }}' was removed", "INVENTORY_ITEM_SAVED": "Inventory '{{ name }}' was saved", "EDIT_PRODUCT_VARIANT": "Edit product variant", "PRODUCT_VARIANT_SAVED": "Product variant saved", "NAME": "Name", "ENABLED": "Enabled", "PRODUCT_TYPE": "Product type", "PRODUCT_CATEGORY": "Product category", "IS_SUBSCRIPTION": "Is subscription", "IS_PURCHASE_AUTOMATICALLY": "Is purchased automatically", "CAN_BE_SOLD": "Can be sold", "CAN_BE_PURCHASED": "Can be purchased", "CAN_BE_CHARGED": "Can be charged", "CAN_BE_RENTED": "Can be rented", "IS_EQUIPMENT": "Is equipment", "TRACK_INVENTORY": "Track inventory", "ADD_OPTION": "Add option", "EDIT_OPTION": "Edit option", "INTERNATIONAL_REFERENCE": "International reference", "CODE": "Code", "NOTES": "Notes", "DESCRIPTION": "Description", "UNIT_COST": "Unit Cost", "UNIT_COST_CURRENCY": "Unit cost currency", "RETAIL_PRICE": "Retail price", "RETAIL_PRICE_CURRENCY": "Retail price currency", "QUANTITY": "Quantity", "TAXES": "Taxes", "BILLING_INVOICING_POLICY": "Billing invoicing policy", "PRODUCT_TYPES": "Product types", "PRODUCT_CATEGORIES": "Product categories", "ORGANIZATION": "Organization", "EDIT_PRODUCT_TYPE": "Edit product type", "ADD_PRODUCT_TYPE": "Add product type", "PRODUCT_TYPE_SAVED": "Product type '{{ name }}' was saved", "PRODUCT_TYPE_DELETED": "Product type '{{ name }}' was removed", "EDIT_PRODUCT_CATEGORY": "Edit product category", "ADD_PRODUCT_CATEGORY": "Add product category", "PRODUCT_CATEGORY_SAVED": "Product category '{{ name }}' was saved", "PRODUCT_CATEGORY_DELETED": "Product category '{{ name }}' was removed", "IMAGE": "Image", "LANGUAGE": "Language", "PRODUCT_VARIANT_DELETED": "Product variant deleted!", "ICON": "Icon", "ADD_VARIANT": "Add variant", "EDIT_VARIANT": "Edit variant", "NO_OPTIONS_LABEL": "(no options)", "OPTIONS": "Options", "SELECT_OR_UPLOAD_IMAGE": "Select or upload image", "SELECT_IMAGE": "Select image", "NO_IMAGE_SELECTED": "No image selected", "URL": "Url", "DIMENSIONS": "Dimensions", "FEATURED_IMAGE_WAS_SAVED": "Featured image was saved!", "IMAGE_SAVED": "Image was saved!", "ADD_GALLERY_IMAGE": "Add gallery image", "SET_FEATURED_IMAGE": "Set featured image", "VIEW_GALLERY": "View gallery", "EDIT_IMAGE": "Edit gallery image", "DELETE_IMAGE": "Delete image", "IMAGE_ASSET_DELETED": "Image asset deleted", "CATEGORY": "Category", "TYPE": "Type", "VIEW_INVENTORY_ITEM": "View inventory item", "TAGS": "Tags", "PRICE": "Price", "SAVE": "Save", "CANCEL": "Cancel", "WIDTH": "<PERSON><PERSON><PERSON>", "HEIGHT": "Height", "IMAGE_ADDED_TO_GALLERY": "Image was added to the gallery", "IMAGES_ADDED_TO_GALLERY": "The Images were added to the gallery", "IMAGE_ASSET_UPDATED": "The image asset was updated", "EDIT_IMAGE_ASSET": "Edit image asset", "WAREHOUSES": "Warehouses", "EMAIL": "Email", "ACTIVE": "Active", "INACTIVE": "Inactive", "LOCATION": "Location", "ADDRESS": "Address", "CREATE_WAREHOUSE": "Create Warehouse", "EDIT_WAREHOUSE": "Edit Warehouse", "WAREHOUSE_CREATED": "Warehouse created", "COULD_NOT_CREATE_WAREHOUSE": "Could not create warehouse", "WAREHOUSE_WAS_CREATED": "Warehouse '{{ name }}' was created", "WAREHOUSE_WAS_DELETED": "Warehouse '{{ name }}' was deleted", "WAREHOUSE_WAS_UPDATED": "Warehouse '{{ name }}' was updated", "CITY": "City", "LOGO": "Logo", "CONTACT": "Contact", "COUNTRY": "Country", "NEW_OPTION_GROUP": "New option group", "OPTION_GROUP_NAME": "Option group name", "OPTION_TRANSLATIONS": "Option translations", "ADD_PRODUCTS": "Add products", "MANAGE_VARIANTS_QUANTITY": "Manage variants", "ADD_PRODUCT": "Add product", "STORES": "Stores", "ADD_STORE": "Add store", "EDIT_STORE": "Edit store", "CREATE_STORE": "Create store", "PHONE": "Phone", "FAX": "Fax", "FISCAL_INFORMATION": "Fiscal information", "WEBSITE": "Website", "MERCHANTS": "Merchants", "CREATE_MERCHANT": "New Merchant", "DELETE_MERCHANT": "Delete Merchant", "EDIT_MERCHANT": "Edit Merchant", "MERCHANT_CREATED_SUCCESSFULLY": "Merchant '{{ name }}' created successfully!", "MERCHANT_DELETED_SUCCESSFULLY": "Merchant '{{ name }}' deleted!", "MERCHANT_UPDATED_SUCCESSFULLY": "Merchant '{{ name }}' updated successfully!", "THIS_FIELD_IS_REQUIRED": "This field is required", "EMAIL_WRONG_FORMAT": "Email is in wrong format", "PHONE_WRONG_FORMAT": "Phone is in wrong format", "SELECTED": "Selected", "SUCCESSFULLY_ADDED_PRODUCTS": "Products were added successfully!", "MAIN": "Main", "INVENTORY": "Inventory", "IMAGE_WAS_DELETED": "Image was deleted"}, "TASKS_PAGE": {"HEADER": "Tasks", "MY_TASK_HEADER": "My Tasks", "TEAM_TASKS_HEADER": "Team's Tasks", "ADD_TASKS": "Add Tasks", "EDIT_TASKS": "Edit Tasks", "EDIT_TASK": "Edit Task", "DELETE_TASK": "Delete Task", "TASKS_TITLE": "Title", "TASKS_DESCRIPTION": "Description", "TASKS_LOADED": "Tasks loaded", "TASK_ADDED": "Task was added", "TASK_UPDATED": "Task was changed", "TASK_DELETED": "Task was removed", "TASKS_PROJECT": "Project", "TASKS_CREATOR": "Created By", "TASK_MEMBERS": "Employees", "TASK_ASSIGNED_TO": "Assigned To", "TASK_TEAMS": "Teams", "TASK_ID": "ID", "TASK_NUMBER": "Task Number", "DUE_DATE": "Due Date", "ESTIMATE": "Estimate", "ESTIMATE_DAYS": "Days", "ESTIMATE_HOURS": "Hours", "ESTIMATE_MINUTES": "<PERSON>s", "TASKS_STATUS": "Status", "TASK_PRIORITY": "Priority", "TASK_SIZE": "Size", "PARENT_TASK": "Parent Task", "TODO": "Todo", "OPEN": "Open", "READY_FOR_REVIEW": "Ready for review", "IN_PROGRESS": "In Progress", "IN_REVIEW": "In Review", "FOR_TESTING": "For Testing", "COMPLETED": "Completed", "BLOCKED": "Blocked", "TASK_VIEW_MODE": "Task View Mode", "PROJECT": "Project", "COMPLETE_SPRINT": "Complete Sprint", "DATE_START": "Date Start", "DATE_END": "Date End", "BACKLOG": "Backlog", "EDIT_SPRINT": "Edit Sprint", "DELETE_SPRINT": "Delete Sprint", "SELECT": "Select", "SPRINTS_SETTINGS": "Sprints Settings", "ARE_YOU_SURE": "Are you sure you want delete sprint", "SETTINGS": "Settings", "MODULE": "<PERSON><PERSON><PERSON>", "SELECT_MODULE": "<PERSON><PERSON><PERSON>(s)"}, "JOBS": {"EMPLOYEE": "Employee", "TITLE": "Title", "DESCRIPTION": "Description", "CREATED_DATE": "Created Date", "STATUS": "Status", "ACTION": "Action", "APPLY": "Apply Manually", "APPLY_AUTO": "Auto Apply", "CLOSED": "Closed", "OPEN": "Open", "APPLIED": "Applied", "COMPLETED": "Completed", "VIEW": "View", "HIDE": "<PERSON>de", "NO_JOBS": "No Jobs found", "JOB_SEARCH": "Job Search", "JOB_DETAILS": "Job Details", "LOAD_MORE": "Load More", "HIDE_ALL_CONFIRM": "Are you sure you want to Hide All jobs?", "ACTIONS": "Actions", "UPWORK": "Upwork", "WEB": "Web", "HOURLY": "Hourly", "FIXED": "Fixed", "LINKEDIN": "LinkedIn", "FILTER": {"TITLE": "Advanced Filter", "SOURCE": "Source", "JOB_TYPE": "Job Type", "JOB_STATUS": "Job Status", "BUDGET": "Budget", "LESS_THAN": "Less than"}, "BROWSE": "Browse", "SEARCH": "Search", "HISTORY": "History", "EMPLOYEES": "Employees", "MATCHINGS": "Matchings", "PROPOSALS_TEMPLATE": "Proposals Template", "APPLY_JOB_TITLE": "Apply for Job"}, "JOB_MATCHING": {"CONFIGURE_EMPLOYEES_TO_JOBS_MATCHING": "Configure Employees to Jobs Matching", "SOURCE": "Source", "PRESET": "Preset", "KEYWORDS": "Keywords", "CATEGORY": "Category", "OCCUPATION": "Occupation", "ADD_NEW_CRITERIONS": "Add New", "FIX_PRICE": "Fix Price", "HOURLY": "Hourly", "SAVE": "Save", "DELETE": "Delete", "CRITERIONS": "Criterions", "DELETE_CRITERION_MESSAGE": "Are you sure that, you want to delete criterion", "SAVE_PRESET_MESSAGE": "Criterions will save for job preset. Are you sure you want to save?"}, "JOB_EMPLOYEE": {"EMPLOYEE": "Employee", "EMPLOYEES": "Employees", "AVAILABLE_JOBS": "Available Jobs", "APPLIED_JOBS": "Applied Jobs", "JOB_SEARCH_STATUS": "Job Search Status", "BROWSE": "Browse", "SEARCH": "Search", "HISTORY": "History", "BILLING_RATE": "Billing Rate", "MINIMUM_BILLING_RATE": "Minimum Billing Rate"}, "PROPOSAL_TEMPLATE": {"PROPOSAL_TEMPLATE": "Proposal Template", "EDIT_PROPOSAL_TEMPLATE": "Edit Proposal Template", "ADD_PROPOSAL_TEMPLATE": "Add Proposal Template", "SELECT_PROPOSAL_TEMPLATE": "Select Proposal Template", "SELECT_EMPLOYEE": "Select employee", "NAME": "Name", "CONTENT": "Content", "EMPLOYEE": "Employee", "DESCRIPTION": "Description", "IS_DEFAULT": "<PERSON>", "CONFIRM_DELETE": "Are you sure that, you want to delete", "PROPOSAL_CREATE_MESSAGE": "Proposal template '{{ name }}' was added", "PROPOSAL_EDIT_MESSAGE": "Proposal template '{{ name }}' was changed", "PROPOSAL_DELETE_MESSAGE": "Proposal template '{{ name }}' was removed", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "Proposal template '{{ name }}' set as default", "PROPOSAL_REMOVE_DEFAULT_MESSAGE": "Proposal template '{{ name }}' remove as default", "YES": "Yes", "NO": "No", "BROWSE": "Browse", "SEARCH": "Search"}, "SPRINTS_PAGE": {"SPRINT_ADDED": "Sprint added", "SPRINT_UPDATED": "Sprint updated", "SPRINT_DELETED": "Sprint deleted", "SPRINT": "Sprint", "ADD_SPRINT_NAME": "Add sprint name"}, "USERS_PAGE": {"HEADER": "Manage Users", "ADD_USER": "Add User", "ADD_EXISTING_USER": "Add Existing User", "ADD_EXISTING_ORGANIZATION": "Add Existing Organization", "ADD_EXISTING_USER_TOOLTIP": "Add user from other organization", "CONVERT_USER_TO_EMPLOYEE": "User successfully converted to employee.", "ROLE": {"SUPER_ADMIN": "Super Admin", "ADMIN": "Admin", "MANAGER": "Manager", "DATA_ENTRY": "Data Entry", "VIEWER": "Viewer", "EMPLOYEE": "Employee", "CANDIDATE": "Candidate", "ROLE": "Role"}, "EDIT_USER": {"HEADER": "Manage User", "EDIT_EXISTING_USER": "Edit Existing User", "MAIN": "Main", "USER_ORGANIZATIONS": "Organizations"}, "REMOVE_USER": "'{{ name }}' was removed", "ACTIVE": "Active", "NOT_STARTED": "Not Started"}, "CONTEXT_MENU": {"TIMER": "Start Timer", "ADD_INCOME": "Income", "ADD_EXPENSE": "Expense", "INVOICE": "Invoice", "ESTIMATE": "Estimate", "PAYMENT": "Payment", "TIME_LOG": "Time Log", "CANDIDATE": "Candidate", "PROPOSAL": "Proposal", "CONTRACT": "Contract", "TEAM": "Team", "TASK": "Task", "CLIENT": "Client", "CONTACT": "Contact", "PROJECT": "Project", "ADD_EMPLOYEE": "Employee", "CHAT": "Support Chat", "FAQ": "FAQ", "HELP": "Help"}, "QUICK_ACTIONS_MENU": {"CREATE_INVOICE": "Create Invoice", "RECEIVED_INVOICES": "Received Invoices", "CREATE_INCOME": "Create Income", "CREATE_EXPENSE": "Create Expense", "CREATE_ESTIMATE": "Create Estimate", "RECEIVED_ESTIMATES": "Received Estimates", "CREATE_PAYMENT": "Create Payment", "ADD_EMPLOYEE": "Add Employee", "ADD_INVENTORY": "Add Inventory", "ADD_EQUIPMENT": "Add Equipment", "ADD_VENDOR": "Add <PERSON>", "ADD_DEPARTMENT": "Add Department", "CREATE_TEAM": "Create Team", "CREATE_TASK": "Create Task", "CREATE_PROJECT": "Create Project", "VIEW_TASKS": "View Tasks", "VIEW_TEAM_TASKS": "View Team's Tasks", "CREATE_CANDIDATE": "Create Candidate", "CREATE_PROPOSAL": "Create Proposal", "CREATE_CONTRACT": "Create Contract", "CREATE_LEAD": "Create Lead", "CREATE_CUSTOMER": "Create Customer", "CREATE_CLIENT": "Create Client", "VIEW_CLIENTS": "View Clients", "START_TIMER": "Start Timer", "STOP_TIMER": "Stop Timer", "TIME_LOG": "Time Log", "VIEW_APPOINTMENTS": "View Appointments", "VIEW_TIME_ACTIVITY": "View Time Activity"}, "QUICK_ACTIONS_GROUP": {"ACCOUNTING": "Accounting", "ORGANIZATION": "Organization", "PROJECT_MANAGEMENT": "Project Management", "JOBS": "Jobs", "CONTACTS": "Contacts", "TIME_TRACKING": "Time Tracking"}, "PROFILE_PAGE": {"FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "EMAIL": "Email", "PASSWORD": "Password", "REPEAT_PASSWORD": "Repeat Password", "ERROR": "Error", "SAVE": "Save", "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "PASSWORDS_DO_NOT_MATCH": "Password Do Not Match!"}}, "INVITE_PAGE": {"USER": {"MANAGE": "Manage User Invites", "HEADER": "Invite Users", "ACTION": "Invite Users"}, "EMPLOYEE": {"MANAGE": "Manage Employee Invites", "HEADER": "Invite Employees", "ACTION": "Invite Employees"}, "CANDIDATE": {"MANAGE": "Manage Candidate In<PERSON>tes", "HEADER": "In<PERSON><PERSON>", "ACTION": "In<PERSON><PERSON>"}, "SENT": "{{total}} In<PERSON><PERSON>.", "IGNORED": "{{total}} In<PERSON><PERSON>. {{ignored}} were already invited and have been ignored.", "INVITATION_EXPIRATION_OPTIONS": {"DAY": "1 Day", "WEEK": "7 Days", "TWO_WEEK": "14 Days", "MONTH": "30 Days", "NEVER": "Never"}}, "INVOICES_PAGE": {"SENDER": "Sender", "BROWSE": "Browse", "COMMENT": "Comment", "COMMENTS": "Comments", "HEADER": "Invoices", "INVOICE_NUMBER": "Invoice Number", "ESTIMATE_NUMBER": "Estimate Number", "INVOICE_DATE": "Invoice Date", "ESTIMATE_DATE": "Estimate Date", "DUE_DATE": "Due Date", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "DISCOUNT": "Discount", "CONTACT": "Contact", "TOTAL_VALUE": "Total Value", "PAID_STATUS": "Paid Status", "TAX": "Tax", "TAX_2": "Tax 2", "INVOICE_ACCEPTED": "Invoice Accepted", "INVOICE_REJECTED": "Invoice Rejected", "INVOICES_ADD_INVOICE": "Invoice Added", "INVOICES_ADD_ESTIMATE": "Estimated Added", "INVOICES_EDIT_INVOICE": "Invoice Edited", "INVOICES_EDIT_ESTIMATE": "Estimate Edited", "INVOICES_DUPLICATE_INVOICE": "Invoice Duplicated", "INVOICES_DUPLICATE_ESTIMATE": "Estimate Duplicated", "INVOICES_DELETE_INVOICE": "Invoice Deleted", "INVOICES_DELETE_ESTIMATE": "Estimate Deleted", "INVOICES_SELECT_INVOICE_DATE": "Invoice Date", "INVOICES_SELECT_DUE_DATE": "Due Date", "INVOICES_SELECT_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "INVOICES_SELECT_DISCOUNT_VALUE": "Discount Value", "INVOICES_SELECT_DISCOUNT": "Discount", "INVOICES_SELECT_PAID": "Paid", "INVOICES_TAXES": "Taxes", "INVOICES_SELECT_TERMS": "Terms", "ADD_INVOICE": "Add Invoice", "ADD_ESTIMATE": "Add Estimate", "EDIT_INVOICE": "Edit Invoice", "EDIT_ESTIMATE": "Edit Estimate", "VIEW_INVOICE": "View Invoice", "VIEW_ESTIMATE": "View Estimate", "SELECT_EMPLOYEE": "Select Employee", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_PRODUCT": "Select Product", "INVALID_DATES": "Invalid Dates", "INVOICE_NUMBER_DUPLICATE": "Invoice Number already exists", "DISCOUNT_TYPE": "Discount Type", "TAX_TYPE": "Tax Type", "TAX_VALUE": "Tax Value", "PERCENT": "Percent", "FLAT": "Flat", "SUBTOTAL": "Subtotal", "TOTAL": "Total", "NAME": "Name", "PAID": "Paid", "NOT_PAID": "Not Paid", "RECEIVED_INVOICES": "Received Invoices", "RECEIVED_ESTIMATES": "Received Estimates", "SEND_INVOICE": "Invoice Sent", "SEND_ESTIMATE": "Estimate Sent", "APPLY_TAX": "Apply Tax", "APPLY_DISCOUNT": "Apply Discount", "APPLIED": "Applied", "NOT_APPLIED": "Not Applied", "STATUS": "Status", "SET_STATUS": "SET STATUS", "SHOW_COLUMNS": "Show Columns", "ITEM": "<PERSON><PERSON>", "SETTINGS": "Settings", "SHOW_HIDE_COLUMNS": "Show/Hide Columns", "INVOICES_PER_PAGE": "Invoices Per Page", "ESTIMATES_PER_PAGE": "Estimates Per Page", "APPLY_DISCOUNT_AFTER_TAX": "Apply discount after tax", "SELECT_INVOICE_TO_VIEW_HISTORY": "Select an invoice to view its history", "INVOICE_SENT_TO": "Invoice sent to '{{ name }}'", "ESTIMATE_SENT_TO": "Estimate sent to '{{ name }}'", "INVOICE": "Invoice", "ESTIMATE": "Estimate", "ACTIONS": "Actions", "HISTORY": "History", "SEARCH": "Search", "NUMBER": "Number", "FROM": "FROM", "TO": "TO", "DATE": "Date", "YES": "Yes", "NO": "No", "ALREADY_PAID": "Already Paid", "AMOUNT_DUE": "Amount Due", "INVOICED_REMAINING_AMOUNT": "Invoiced Remaining Amount", "TAX_CALCULATION_TYPE": "Tax Calculation Type", "SIMPLE": "Simple", "COMPOSED": "Composed", "INVOICE_TYPE": {"INVOICE_TYPE": "Invoice Type", "ESTIMATE_TYPE": "Estimate Type", "GENERATE_INVOICE_ITEMS": "Generate Invoice Items", "GENERATE_ESTIMATE_ITEMS": "Generate Estimate Items", "GENERATE_FOR_UNINVOICED_EXPENSES": "Generate for all uninvoiced expenses", "BY_EMPLOYEE_HOURS": "By Employee Hours", "BY_PROJECT_HOURS": "By Project Hours", "BY_TASK_HOURS": "By Task Hours", "BY_PRODUCTS": "By Products", "BY_EXPENSES": "By Expenses", "DETAILED_ITEMS": "Detailed Items", "SELECT_INVOICE_TYPE": "Select an invoice type", "SELECT_ESTIMATE_TYPE": "Select an estimate type", "SELECT_PROJECTS": "Select projects", "SELECT_TASKS": "Select tasks", "SELECT_PRODUCTS": "Select products", "SELECT_EXPENSES": "Select expenses"}, "INVOICE_ITEM": {"ITEM_NUMBER": "Item Number", "TASK": "Task", "NAME": "Name", "DESCRIPTION": "Description", "PRICE": "Price", "QUANTITY": "Quantity", "TOTAL_VALUE": "Total Value", "EMPLOYEE": "Employee", "HOURLY_RATE": "Hourly Rate", "HOURS_WORKED": "Hours Worked", "PROJECT": "Project", "PRODUCT": "Product", "EXPENSE": "Expense", "NO_ITEMS": "Please add invoice items", "INVALID_ITEM": "Invalid Invoice Item", "EMPLOYEE_VALUE": "Employee cannot be empty", "PROJECT_VALUE": "Project cannot be empty", "TASK_VALUE": "Task cannot be empty", "ITEM": "<PERSON><PERSON>"}, "SEND": {"CONFIRMATION_INVOICE": "Send this invoice to", "CONFIRMATION_ESTIMATE": "Send this estimate to", "ALREADY_SENT_INVOICE": "This invoice has already been sent to", "ALREADY_SENT_ESTIMATE": "This estimate has already been sent to", "NOT_LINKED": "Client does not have an organization", "SENT": "<PERSON><PERSON>", "NOT_SENT": "Not Sent"}, "VIEW": {"FROM": "FROM", "TO": "TO"}, "DOWNLOAD": {"CONFIRMATION_INVOICE": "Download this invoice ?", "CONFIRMATION_ESTIMATE": "Download this estimate ?", "INVOICE_DOWNLOAD": "Invoice downloaded", "ESTIMATE_DOWNLOAD": "Estimate downloaded"}, "EMAIL": {"EMAIL_INVOICE": "Send this invoice by email ?", "EMAIL_ESTIMATE": "Send this estimate by email ?", "EMAIL_SENT": "<PERSON><PERSON>"}, "ESTIMATES": {"HEADER": "Estimates", "ESTIMATE_NUMBER": "Estimate Number", "ESTIMATE_DATE": "Estimate Date", "ACCEPT": "Accept", "REJECT": "Reject", "ACCEPTED": "Accepted", "REJECTED": "Rejected", "ACCEPTED_STATUS": "Accepted Status", "ESTIMATE_CONVERT": "Estimate Converted", "SELECT_ESTIMATE_TO_VIEW_HISTORY": "Select an estimate to view its history", "ESTIMATE_ACCEPTED": "Estimate accepted", "ESTIMATE_REJECTED": "Estimate rejected", "ERROR": "An error occurred", "CONVERTED_TO_INVOICE": "Estimate converted to invoice"}, "PAYMENTS": {"HEADER": "Payments for Invoice", "TOTAL_VALUE": "Total Invoice Value", "RECORD_PAYMENT": "Record Payment", "EDIT_PAYMENT": "Edit Payment", "DELETE_PAYMENT": "Delete Payment", "TOTAL_PAID": "Total Paid", "PAID": "Paid", "PAYMENT_DATE": "Payment Date", "AMOUNT": "Amount", "RECORDED_BY": "Recorded By", "NOTE": "Note", "PAYMENT_ADD": "Payment was added", "PAYMENT_EDIT": "Payment was changed", "PAYMENT_DELETE": "Payment was removed", "PAYMENT_DOWNLOAD": "Payment Downloaded", "STATUS": "Status", "ON_TIME": "On time", "OVERDUE": "Overdue", "NO_PAYMENTS_RECORDED": "No payments recorded", "LEFT_TO_PAY": "Left to pay", "PAYMENT_METHOD": "Payment Method", "SELECT_INVOICE": "Select Invoice", "PAYMENT_AMOUNT_ADDED": "Payment of {{ amount }} {{ currency }} added", "PAYMENT": "Payment", "BANK_TRANSFER": "Bank Transfer", "CASH": "Cash", "CHEQUE": "Cheque", "CREDIT_CARD": "Credit Card", "DEBIT": "Debit", "ONLINE": "Online", "PAYMENTS_FOR_INVOICE": "Payments for invoice", "RECEIVED_FROM": "Received from", "RECEIVER": "Receiver", "SEND_RECEIPT": "Send this receipt to {{ name }} ?", "CONTACT_GREETING": "Hi {{ name }},", "RECEIPT_FOR": "This is your receipt for Invoice {{ invoiceNumber }} for {{ amount }} {{ currency }}.", "BEST_REGARDS": "Best regards the {{ name }} team."}, "INTERNAL_NOTE": {"NOTE_SAVED": "Internal Note was added", "ADD_INTERNAL_NOTE": "Add Internal Note", "ADD_NOTE": "Add note", "NOTE": "Note", "INTERNAL_NOTE": "Internal Note"}, "STATUSES": {"DRAFT": "Draft", "SENT": "<PERSON><PERSON>", "VIEWED": "Viewed", "FULLY_PAID": "<PERSON>y Paid", "PARTIALLY_PAID": "Partially Paid", "OVERPAID": "Overpaid", "VOID": "Void", "ACCEPTED": "Accepted", "REJECTED": "Rejected"}, "ACTION": {"DUPLICATE": "Duplicate", "SEND": "Send", "CONVERT_TO_INVOICE": "Convert to invoice", "EMAIL": "Email", "DELETE": "Delete", "NOTE": "Note", "PAYMENTS": "Payments"}, "PUBLIC_LINK": {"HEADER": "Generate Public Link", "GENERATE": "Generate a link to the {{ text }} that anyone with the link can view.", "ACCESS": "Anyone with access to this link can view the {{ text }}.", "COPY_TO_CLIPBOARD_TOOLTIP": "Copy public link to clipboard"}}, "PAYMENTS_PAGE": {"HEADER": "Payments", "CONTACT": "Contact", "AMOUNT": "Amount", "PAYMENT_DATE": "Payment Date", "RECORDED_BY": "Recorded By", "NOTE": "Note", "STATUS": "Status", "ON_TIME": "On time", "OVERDUE": "Overdue", "PAYMENT_METHOD": "Payment Method", "PROJECT": "Project", "TAGS": "Tags"}, "HEADER": {"SELECT_EMPLOYEE": "Select Employee", "SELECT_A_DATE": "Select A Date", "SELECT_AN_ORGANIZATION": "Select An Organization", "SELECT_PROJECT": "Select Project", "SELECT_TEAM": "Select Team"}, "HEADER_TITLE": {"FOR": "for", "FROM": "from"}, "PAGE_NOT_FOUND": {"404_PAGE_NOT_FOUND": "Page Not Found", "TAKE_ME_HOME": "Take me home", "THE_PAGE_YOU_WERE_LOOKING_FOR_DOES_NOT_EXIST": "The page you were looking for doesn't exist", "REDIRECT_TO_HOME": "You'll be redirected to home page soon"}, "HELP_PAGE": {"HELP": "Help", "KNOWLEDGE_BASE": "Knowledge base", "CHOSE_ICON": "Chose icon to set", "CREATED_AT": "created at", "WRITTEN_BY": "written by", "EMPLOYEES": "employees", "ONLY_FOR_EMPLOYEES": "Only for employees", "DRAFT": "draft", "ADD_ARTICLE": "Add article", "CHOOSE_ANY_CATEGORY": "Choose any category", "ARTICLES": "articles", "REMOVE_ARTICLE": "Remove Article", "ARE_YOU_SURE": "Are you sure? This cannot be undone.", "DESCRIPTION": "Description", "ARTICLE_TEXT": "Article text", "EDIT_ARTICLE": "Edit Article", "MANAGE_CATEGORY": "Manage Category", "ADD_CATEGORY": "Add Category", "EDIT_BASE": "Edit Knowledge Base", "DELETE_BASE": "Delete Base", "LANGUAGE": "Language", "PUBLISH_STATUS": "Publish Status", "PRIVATE_STATUS": "Private Status", "COLOR": "Color", "NAME_CATEGORY": "Name of the category", "NAME_ARTICLE": "Name of the Article", "ADD_BASE": "Add Knowledge Base", "MANAGE_BASE": "Manage Knowledge Base", "NAME_OF_THE_BASE": "Name of the base", "REMOVE_CATEGORY": "Remove Category", "REMOVE_BASE": "Remove Base", "CLEAR": "Clear", "SEARCH_BY_NAME": "Search by name", "FILTER_BY_AUTHOR": "Filter by author", "CATEGORY_EDIT_ADDED": "Category was added", "CATEGORY_EDIT_UPDATED": "Category was changed", "CATEGORY_EDIT_DELETED": "Category was removed", "ERRORS": {"NAME_REQUIRE": "Name of base is require.", "MAXIMUM_LENGTH": "Maximum length exceeded 255 characters.", "ARTICLE_DESC_REQUIRE": "Article description is require", "ARTICLE_NAME_REQUIRE": "Name of the article is require."}}, "PROJECT_MANAGEMENT_PAGE": {"THIS_TAB_WILL_SHOW_PROJECT_MANAGEMENT_CHARTS_AND_AGGREGATED_DATA": "This tab will show project management charts and aggregated data.", "PROJECT_MODULE": {"PROJECT": "Project", "SELECT_PROJECT": "Select a project", "STATUS": "Status", "TEAM_MEMBERS": "Team Members", "TEAMS": "Teams", "SELECT_TEAMS": "Select Teams", "CHOOSE_TEAMS": "Choose teams", "NAME": "Module Name", "ENTER_NAME": "Enter the module name", "PARENT_MODULE": "<PERSON><PERSON>", "SELECT_PARENT": "Select a parent module", "START_DATE": "Start Date", "SELECT_START_DATE": "Select start date", "END_DATE": "End Date", "SELECT_END_DATE": "Select end date", "DESCRIPTION": "Description", "MANAGER": "Manager", "SELECT_MANAGER": "Select a manager", "ORGANIZATION_SPRINTS": "Organization Sprints", "SELECT_SPRINTS": "Select sprints", "IS_FAVORITE": "Favorite"}}, "SETTINGS_FEATURES": {"INVOICE": "Invoice", "INCOME": "Income", "EXPENSE": "Expense", "PAYMENT": "Payment", "PROPOSAL": "Proposal", "SALES_PIPELINE": "Sales Pipeline", "TASK_DASHBOARD": " Task Dashboard", "JOBS": "Jobs", "EMPLOYEES": "Employees", "TIME_ACTIVITY": "Time Activity", "TIMESHEET": "Timesheet", "APPOINTMENT_SCHEDULE": "Appointment & Schedule", "CANDIDATE": "Candidate", "MANAGE_ORGANIZATION": "Manage Organization", "PRODUCT_INVENTORY": "Product Inventory", "PROJECT": "Project", "ORGANIZATION_TEAM": "Organization Team", "ORGANIZATION_DOCUMENT": "Organization Document", "LEAD_CUSTOMER_CLIENT": "Lead, Customer & Client", "GOAL_AND_OBJECTIVE": "Goal and Objective", "ALL_REPORT": "All Report", "USERS": "Users", "ORGANIZATIONS": "Organizations", "APPS_INTEGRATIONS": "Apps & Integrations", "EMAIL_HISTORY": "Email History", "SETTING": "Setting", "ENTITY_IMPORT_EXPORT": "Entity Import & Export", "CUSTOM_SMTP": "Custom SMTP", "ROLES_PERMISSIONS": "Roles & Permissions", "TIME_TRACKING": "Time Tracking", "ESTIMATE": "Estimate", "DASHBOARD": "Dashboard"}, "SETTINGS_FEATURES_DESCRIPTION": {"INVOICE": {"MANAGE_INVOICE_CREATE_FIRST_INVOICE": "Manage Invoice, Create First Invoice"}, "INCOME": {"CREATE_FIRST_INCOME": "Create First Income"}, "EXPENSE": {"CREATE_FIRST_EXPENSE": "Create First Expense"}, "PAYMENT": {"MANAGE_PAYMENT_CREATE_FIRST_PAYMENT": "Manage Payment, Create First Payment"}, "PROPOSAL": {"MANAGE_PROPOSAL_REGISTER_FIRST_PROPOSAL": "Manage Proposal, Register First Proposal"}, "SALES_PIPELINE": {"CREATE_SALES_PIPELINE": "Create Sales Pipeline"}, "TASK_DASHBOARD": {"TASK_DASHBOARD": "Task Dashboard"}, "JOBS": {"JOB_SEARCH_JOBS_MATCHING": "Job Search & Jobs Matching"}, "EMPLOYEES": {"MANAGE_EMPLOYEES_ADD_OR_INVITE_EMPLOYEES": "Manage Employees, Add or Invite Employees"}, "TIME_ACTIVITY": {"MANAGE_TIME_ACTIVITY_SCREENSHOTS_APP_VISITED_SITES_ACTIVITIES": "Manage Time Activity, Screenshots, App, Visited Sites, Activities"}, "TIMESHEET": {"MANAGE_EMPLOYEE_TIMESHEET_DAILY_WEEKLY_CALENDAR_CREATE_FIRST_TIMESHEET": "Manage Employee Timesheet Daily, Weekly, Calendar, Create First Timesheet"}, "APPOINTMENT_SCHEDULE": {"EMPLOYEE_APPOINTMENT_SCHEDULES_BOOK_PUBLIC_APPOINTMENT": "Employee Appointment, Schedules & Book Public Appointment"}, "CANDIDATE": {"MANAGE_CANDIDATES_INTERVIEWS_INVITES": "Manage Candidates, Interviews & Invites"}, "MANAGE_ORGANIZATION": {"MANAGE_ORGANIZATION_DETAILS_LOCATION_AND_SETTINGS": "Manage Organization Details, Location and Settings"}, "PRODUCT_INVENTORY": {"MANAGE_PRODUCT_INVENTORY_CREATE_FIRST_PRODUCT": "Manage Product Inventory, Create First Product"}, "PROJECT": {"MANAGE_PROJECT_CREATE_FIRST_PROJECT": "Manage Project, Create First Project"}, "ORGANIZATION_TEAM": {"MANAGE_ORGANIZATION_TEAM_CREATE_FIRST_TEAM": "Manage Organization Team, Create First Team"}, "ORGANIZATION_DOCUMENT": {"MANAGE_ORGANIZATION_DOCUMENT_CREATE_FIRST_DOCUMENT": "Manage Organization Document, Create First Document"}, "LEAD_CUSTOMER_CLIENT": {"MANAGE_LEADS_CUSTOMERS_AND_CLIENTS_CREATE_FIRST_CUSTOMER/CLIENTS": "Manage Leads, Customers and Clients, Create First Customer/Clients"}, "GOAL_AND_OBJECTIVE": {"MANAGE_GOALS_AND_OBJECTIVES": "Manage Goals and Objectives"}, "ALL_REPORT": {"MANAGE_EXPENSE_WEEKLY_TIME_ACTIVITY_AND_ETC_REPORTS": "Manage Expense, Weekly, Time & Activity and etc reports"}, "USERS": {"MANAGE_TENANT_USERS": "Manage Tenant Users"}, "ORGANIZATIONS": {"MANAGE_TENANT_ORGANIZATIONS": "Manage Tenant Organizations"}, "APPS_INTEGRATIONS": {"MANAGE_AVAILABLE_APPS_INTEGRATIONS_LIKE_UPWORK_HUBSTAFF": "Manage Available Apps & Integrations Like Upwork & Hubstaff"}, "EMAIL_HISTORY": {"MANAGE_EMAIL_HISTORY": "Manage Email History"}, "SETTING": {"MANAGE_SETTING": "Manage Setting"}, "ENTITY_IMPORT_EXPORT": {"MANAGE_ENTITY_IMPORT_AND_EXPORT": "Manage Entity Import and Export"}, "CUSTOM_SMTP": {"MANAGE_TENANT_ORGANIZATION_CUSTOM_SMTP": "Manage Tenant & Organization Custom SMTP"}, "ROLES_PERMISSIONS": {"MANAGE_ROLES_PERMISSIONS": "Manage Roles & Permissions"}, "TIME_TRACKING": {"DOWNLOAD_DESKTOP_APP_CREATE_FIRST_TIMESHEET": "Download Desktop App, Create First Timesheet"}, "ESTIMATE": {"MANAGE_ESTIMATE_CREATE_FIRST_ESTIMATE": "Manage Estimate, Create First Estimate"}, "DASHBOARD": {"GO_TO_DASHBOARD_MANAGE_EMPLOYEE_STATISTICS_TIME_TRACKING_DASHBOARD": "Go to dashboard, Manage Employee Statistics, Time Tracking Dashboard"}}, "SETTINGS_FEATURES_TEXT": {"INVOICE": {"INVOICE_RECEIVED": "Invoice Received"}, "INCOME": {"": ""}, "EXPENSE": {"EMPLOYEE_RECURRING_EXPENSE": "Employee Recurring Expense", "ORGANIZATION_RECURRING_EXPENSES": "Organization Recurring Expenses"}, "PAYMENT": {"": ""}, "PROPOSAL": {"PROPOSAL_TEMPLATE": "Proposal Template"}, "SALES_PIPELINE": {"SALES_PIPELINE_DEAL": "Sales Pipeline Deal"}, "TASK_DASHBOARD": {"TEAM_TASK_DASHBOARD": "Team Task Dashboard", "MY_TASK_DASHBOARD": "My Task Dashboard"}, "JOBS": {"": ""}, "EMPLOYEES": {"EMPLOYEE_LEVEL": "Employee Level", "EMPLOYEE_POSITION": "Employee Position", "EMPLOYEE_TIME_OFF": "Employee Time Off", "EMPLOYEE_APPROVAL": "Employee Approval", "EMPLOYEE_APPROVAL_POLICY": "Employee Approval Policy"}, "TIME_ACTIVITY": {"": ""}, "TIMESHEET": {"": ""}, "APPOINTMENT_SCHEDULE": {"": ""}, "CANDIDATE": {"MANAGE_INTERVIEW": "Manage Interview", "MANAGE_INVITE": "Manage Invite"}, "MANAGE_ORGANIZATION": {"HELP_CENTER": "Help Center", "ORGANIZATION_TAG": "Organization Tag", "ORGANIZATION_EQUIPMENT": "Organization Equipment", "ORGANIZATION_VENDOR": "Organization Vendor", "ORGANIZATION_DEPARTMENT": "Organization Department", "ORGANIZATION_EMPLOYMENT_TYPE": "Organization Employment Type"}, "PRODUCT_INVENTORY": {"": ""}, "PROJECT": {"": ""}, "ORGANIZATION_TEAM": {"": ""}, "ORGANIZATION_DOCUMENT": {"": ""}, "LEAD_CUSTOMER_CLIENT": {"": ""}, "GOAL_AND_OBJECTIVE": {"GOAL_TIME_FRAME_KPI": "Goal Time Frame & KPI"}, "ALL_REPORT": {"": ""}, "USERS": {"": ""}, "ORGANIZATIONS": {"": ""}, "APPS_INTEGRATIONS": {"": ""}, "EMAIL_HISTORY": {"CUSTOM_EMAIL_TEMPLATE": "Custom Email Template"}, "SETTING": {"FILE_STORAGE": "File Storage", "SMS_GATEWAY": "SMS Gateway"}, "ENTITY_IMPORT_EXPORT": {"": ""}, "CUSTOM_SMTP": {"": ""}, "ROLES_PERMISSIONS": {"": ""}, "TIME_TRACKING": {"": ""}, "ESTIMATE": {"ESTIMATE_RECEIVED": "Estimate Received"}, "DASHBOARD": {"": ""}}, "ABOUT_PAGE": {"ABOUT": "About"}, "FOOTER": {"BY": "by", "RIGHTS_RESERVED": "All rights reserved.", "PRESENT": "Present", "TERMS_OF_SERVICE": "Terms Of Service", "PRIVACY_POLICY": "Privacy Policy"}, "TOASTR": {"TITLE": {"SUCCESS": "Success", "ERROR": "Error", "INFO": "Info", "WARNING": "Warning", "MAX_LIMIT_REACHED": "Max limit reached"}, "MESSAGE": {"ERRORS": "Please check the form for errors", "PROJECT_LOAD": "Could Not Load Projects", "COPIED": "Link copied to clipboard", "INVITES_LOAD": "Could Not Load Invites", "INVITES_RESEND": "In<PERSON><PERSON> has been resent to '{{ email }}'.", "INVITES_DELETE": "'{{ email }}' was removed", "EMPLOYEE_DEPARTMENT_ADDED": "Employee added to department", "EMPLOYEE_DEPARTMENT_REMOVED": "Employee removed from department", "EMPLOYEE_PROJECT_ADDED": "Employee added to project", "EMPLOYEE_PROJECT_REMOVED": "Employee removed from project", "EMPLOYEE_CLIENT_ADDED": "Employee added to the client", "EMPLOYEE_CLIENT_REMOVED": "Employee removed from the client", "EMPLOYEE_EDIT_ERROR": "Error in editing employee", "EMPLOYEE_PROFILE_UPDATE": "Profile '{{name}}' was changed", "EMPLOYEE_LEVEL_UPDATE": "Employee Level '{{ name }}' was changed", "EMPLOYEE_ADDED": "Employee '{{name}}' added to '{{organization}}'", "EMPLOYEE_INACTIVE": "Employee '{{name}}' set as inactive.", "EMPLOYEE_ACTIVE": "Employee '{{name}}' set as active.", "EMPLOYEE_JOB_STATUS_ACTIVE": "Employee '{{name}}' job status set as active.", "EMPLOYEE_JOB_STATUS_INACTIVE": "Employee '{{name}}' job status set as inactive.", "EMPLOYEE_TIME_TRACKING_ENABLED": "Enabled time tracking for '{{name}}'.", "EMPLOYEE_TIME_TRACKING_DISABLED": "Disabled time tracking for '{{name}}'.", "CONFIRM": "Confirm", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "Are you sure you want to resend the invite to", "OK": "OK", "CANCEL": "Cancel", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "Are you sure you want to change the", "PERMISSION_UPDATED": "{{ permissionName }} permission updated for {{ roleName }}", "ROLE_CREATED": "{{ name }} successfully created", "ROLE_DELETED": "{{ name }} successfully deleted", "ROLE_CREATED_ERROR": "Error while creating {{ name }} role", "ROLE_DELETED_ERROR": "Error while deleting {{ name }} role", "PERMISSION_UPDATE_ERROR": "There was an error in updating the permissions, please refresh & try again", "REGISTER_PROPOSAL_NO_EMPLOYEE_MSG": "Employee is required!", "NEW_ORGANIZATION_PROJECT_INVALID_NAME": "Invalid input", "NEW_ORGANIZATION_TEAM_INVALID_NAME": "Team name and members are required", "NEW_ORGANIZATION_VENDOR_INVALID_NAME": "Vendor name is required", "NEW_ORGANIZATION_EXPENSE_CATEGORY_INVALID_NAME": "Expense category name is required", "NEW_ORGANIZATION_POSITION_INVALID_NAME": "Position name is required", "NEW_ORGANIZATION_EMPLOYEE_LEVEL_INVALID_NAME": "Employee Level is required", "NEW_ORGANIZATION_INVALID_EMPLOYMENT_TYPE": "Employment type name is required", "NEW_ORGANIZATION_DEPARTMENT_INVALID_NAME": "Department name is required", "FAVORITE_ADDED": "{{ name }} added to favorites", "FAVORITE_REMOVED": "{{ name }} removed from favorites", "FAVORITE_ERROR": "Error updating favorite status", "NEW_ORGANIZATION_CLIENT_INVALID_DATA": "Invalid input", "NEW_ORGANIZATION_AWARD_INVALID_NAME": "Vendor name and year are required", "NEW_ORGANIZATION_LANGUAGE_INVALID_NAME": "Language name and level are required", "MAIN_ORGANIZATION_UPDATED": "'{{ name }}' info was changed", "CANDIDATE_SKILL_REQUIRED": "Skill name is required", "CANDIDATE_EDIT_CREATED": "Successfully Created", "CANDIDATE_EDIT_UPDATED": "Successfully updated", "CANDIDATE_EDIT_DELETED": "Successfully deleted", "CANDIDATE_EDUCATION_REQUIRED": "Education information is required", "CANDIDATE_FEEDBACK_REQUIRED": "Feedback information is required", "CANDIDATE_FEEDBACK_ABILITY": "All interviewers have already left feedback", "CANDIDATE_EXPERIENCE_REQUIRED": "Experience information is required", "CANDIDATE_DOCUMENT_REQUIRED": "Document information is required", "CANDIDATE_SKILLS_REQUIRED": "<PERSON><PERSON>'s name is required", "CANDIDATE_PROFILE_UPDATE": "Profile '{{name}}' was changed.", "NAME_REQUIRED": "Name is required!", "CONTACT_TYPE_REQUIRED": "Contact Type is required!", "EMAIL_REQUIRED": "Email is required!", "EMAIL_SHOULD_BE_REAL": "Email should be a real one!", "PHONE_REQUIRED": "Phone is required!", "PHONE_CONTAINS_ONLY_NUMBERS": "Phone should only contain numbers!", "SOMETHING_BAD_HAPPENED": "Something bad happened!", "EMAIL_TEMPLATE_SAVED": "Email template '{{ templateName }}' was saved", "DELETED": "Successfully deleted", "CREATED": "Successfully created", "UPDATED": "Successfully updated", "MOVED_BASE": "Base was moved", "MOVED_CATEGORY": "Category was moved", "CREATED_BASE": "Base '{{ name }}' was added", "EDITED_BASE": "Base '{{ name }}' was changed", "DELETED_BASE": "Base '{{ name }}' was removed", "DELETED_CATEGORY": "Category '{{ name }}' was removed", "CREATED_CATEGORY": "Category was added", "EDIT_ADD_CATEGORY": "Category '{{ name }}' was added", "EDITED_CATEGORY": "Category '{{ name }}' was changed", "HELP_ARTICLE_CREATED": "Article was added", "HELP_ARTICLE_UPDATED": "Article '{{ name }}' was changed", "HELP_ARTICLE_DELETED": "Article '{{ name }}' was removed", "PIPELINE_CREATED": "Pipeline '{{ name }}' was added", "PIPELINE_UPDATED": "Pipeline '{{ name }}' was changed", "PIPELINE_DELETED": "Pipeline '{{ name }}' was removed", "OBJECTIVE_ADDED": "Objective was added", "OBJECTIVE_DELETED": "Objective was removed", "OBJECTIVE_UPDATED": "Objective was changed", "KEY_RESULT_ADDED": "Key Result was added", "KEY_RESULT_DELETED": "Key Result was removed", "KEY_RESULT_UPDATED": "Key Result was changed", "TIME_FRAME_CREATED": "Time frame '{{ name }}' was added", "TIME_FRAME_UPDATED": "Time frame '{{ name }}' was changed", "TIME_FRAME_DELETED": "Time frame '{{ name }}' was removed", "KPI_CREATED": "KPI was added", "KPI_UPDATED": "KPI was changed", "KPI_DELETED": "KPI was removed", "EDIT_PAST_INTERVIEW": "Editing past interviews is prohibited", "ARCHIVE_INTERVIEW": "This interview has already been archived", "DELETE_PAST_INTERVIEW": "Deleting past interviews is prohibited", "GOAL_GENERAL_SETTING_UPDATED": "Goal General settings updated", "MAX_OBJECTIVE_LIMIT": "You cannot create any more Objectives. Please change maximum objective limit to add more objectives.", "MAX_KEY_RESULT_LIMIT": "You cannot create any more Key Results for this Objective. Please change maximum key result limit to add more Key Results.", "CUSTOM_SMTP_ADDED": "Smtp settings successfully created", "CUSTOM_SMTP_UPDATED": "Smtp settings successfully updated", "JOB_MATCHING_SAVED": "Criterion was changed", "JOB_MATCHING_ERROR": "Error while saving criterion, Please try aging", "JOB_MATCHING_DELETED": "Criterion was removed", "APPROVAL_POLICY_CREATED": "Approval policy '{{ name }}' was added", "APPROVAL_POLICY_UPDATED": "Approval policy '{{ name }}' was changed", "APPROVAL_POLICY_DELETED": "Approval policy '{{ name }}' was removed", "APPROVAL_POLICY_ALREADY_EXISTS": "Approval policy '{{ name }}' already exists", "CANDIDATE_CREATED": "Candidate '{{ name }}' was added to '{{ organization }}'", "CANDIDATE_ARCHIVED": "Candidate '{{ name }}' set as archived.", "CANDIDATE_REJECTED": "Candidate '{{ name }}' set as rejected.", "CANDIDATE_HIRED": "Candidate '{{ name }}' set as hired.", "CANDIDATE_DELETED": "Candidate '{{ name }}' was removed", "PRESET_SAVED": "Preset successfully saved", "JOB_APPLIED": "Job applied successfully", "JOB_HIDDEN": "Job hidden successfully", "ORGANIZATION_LOCATION_UPDATED": "'{{ name }}' organization location was changed", "ORGANIZATION_INFO_UPDATED": "'{{ name }}' organization main info was changed", "ORGANIZATION_SETTINGS_UPDATED": "'{{ name }}' organization settings was changed", "ORGANIZATION_TASK_SETTINGS_UPDATE_ERROR": "Some error occurred while trying to updating organization task setting.", "SETTINGS_SAVED": "Setting<PERSON> saved successfully", "KEY_RESULTS_CREATED": "Key Results Created", "INVITE_EMAIL_DELETED": "{{ name }} has been deleted.", "HOLIDAY_ERROR": "Unable to get holidays", "INTERVAL_ERROR": "Please pick correct dates and try again", "PROFILE_UPDATED": "Your profile was changed", "PERSONAL_QUALITIES_CREATED": "Personal Qualities '{{ name }}' was added", "PERSONAL_QUALITIES_UPDATED": "Personal Qualities '{{ name }}' was changed", "PERSONAL_QUALITIES_DELETED": "Personal Qualities '{{ name }}' was removed", "TECHNOLOGY_STACK_CREATED": "Technology Stack '{{ name }}' was added", "TECHNOLOGY_STACK_UPDATED": "Technology Stack '{{ name }}' was changed", "TECHNOLOGY_STACK_DELETED": "Technology Stack '{{ name }}' was removed", "ARCHIVE_INTERVIEW_SET": "'{{ name }}' set as archived.", "INTERVIEW_UPDATED": "'{{ name }}' was changed", "INTERVIEW_DELETED": "'{{ name }}' was removed", "INTERVIEW_FEEDBACK_CREATED": "Feedback was added for '{{ name }}'.", "CANDIDATE_EDUCATION_CREATED": "Education '{{ name }}' was added", "CANDIDATE_EDUCATION_UPDATED": "Education '{{ name }}' was changed", "CANDIDATE_EDUCATION_DELETED": "Education '{{ name }}' was removed", "CANDIDATE_EXPERIENCE_CREATED": "Experience '{{ name }}' was added", "CANDIDATE_EXPERIENCE_UPDATED": "Experience '{{ name }}' was changed", "CANDIDATE_EXPERIENCE_DELETED": "Experience '{{ name }}' was removed", "CANDIDATE_SKILL_CREATED": "Skill '{{ name }}' was added", "CANDIDATE_SKILL_UPDATED": "Skill '{{ name }}' was changed", "CANDIDATE_SKILL_DELETED": "Skill '{{ name }}' was removed", "CANDIDATE_DOCUMENT_CREATED": "Document '{{ name }}' was added", "CANDIDATE_DOCUMENT_UPDATED": "Document '{{ name }}' was changed", "CANDIDATE_DOCUMENT_DELETED": "Document '{{ name }}' was removed", "CANDIDATE_INTERVIEW_CREATED": "Interview '{{ name }}' was added", "CANDIDATE_INTERVIEW_UPDATED": "Interview '{{ name }}' was changed", "CANDIDATE_INTERVIEW_DELETED": "Interview '{{ name }}' was removed", "CANDIDATE_FEEDBACK_CREATED": "Feedback was added", "CANDIDATE_FEEDBACK_UPDATED": "Feedback was changed", "CANDIDATE_FEEDBACK_DELETED": "Feedback was removed", "RECURRING_EXPENSE_SET": "Recurring expense set for '{{ name }}'", "RECURRING_EXPENSE_UPDATED": "Recurring expense was changed for '{{ name }}'", "RECURRING_EXPENSE_DELETED": "Recurring expense was removed for '{{ name }}'", "IMAGE_UPDATED": "The image has been updated", "ORGANIZATION_PAGE_UPDATED": "Page was changed for '{{ name }}'", "SCREENSHOT_DELETED": "Screenshot for '{{ name }}' removed from '{{ organization }}'", "TIME_LOG_DELETED": "Time log for '{{ name }}' removed from '{{ organization }}'", "TIME_LOGS_DELETED": "Time logs removed from '{{ organization }}'", "BUCKET_CREATED": "'{{ bucket }}' for '{{ region }}' has been created successfully", "AUTHORIZED_TO_WORK": "{{ name }} is authorized to work", "EMAIL_VERIFICATION_NOT_VALID": "Sorry, this verification link is not valid.", "EMAIL_VERIFICATION_VALID": "Congrats! Your email is now verified.", "SCREEN_CAPTURE_CHANGED": "Screen capture status changed for {{ name }}", "MODULE_CREATED": "<PERSON><PERSON><PERSON> created successfully!", "MODULE_UPDATED": "Module updated successfully!", "MODULE_SAVE_ERROR": "Failed to save the module. Please try again.", "PASSWORD_REQUIRED": "Password is required."}}, "ACCEPT_INVITE": {"ACCEPT_INVITE_FORM": {"FULL_NAME": "Full Name", "ENTER_YOUR_FULL_NAME": "Enter Your Full Name", "PASSWORD": "Password", "REPEAT_PASSWORD": "Repeat Password", "AGREE_TO": "Agree to", "TERMS_AND_CONDITIONS": "Terms & Conditions", "ADD_ORGANIZATION": "Add Organization", "COMPLETE_REGISTRATION": "Complete Registration", "PASSWORDS_DO_NOT_MATCH": "Password Do Not Match!"}, "INVALID_INVITE": "Either you entered an incorrect URL or the invitation has expired", "HEADING": "Accept Invitation to {{ organizationName }}", "SUB_HEADING": "Complete your registration {{ email }}", "INVITATION_NO_LONGER_VALID": "This invitation is no longer valid", "ACCOUNT_CREATED": "Your account has been created, please login", "COULD_NOT_CREATE_ACCOUNT": "Could not create your account"}, "NOTES": {"INCOME": {"ADD_INCOME": "Income was added for '{{ name }}'", "EDIT_INCOME": "Income was changed for '{{ name }}'", "DELETE_INCOME": "Income was removed for '{{ name }}'", "INCOME_ERROR": "{{ error }}"}, "INVOICE": {"ADD_INVOICE": "Invoice added for '{{ name }}'", "EDIT_INVOICE": "Invoice edited for '{{ name }}'", "DELETE_INVOICE": "Invoice deleted for '{{ name }}'", "INVOICE_ERROR": "{{ error }}"}, "EXPENSES": {"ADD_EXPENSE": "Expense added for '{{ name }}'", "OPEN_EDIT_EXPENSE_DIALOG": "Expense edited for '{{ name }}'", "DELETE_EXPENSE": "Expense deleted for '{{ name }}'", "EXPENSES_ERROR": "{{ error }}"}, "PROPOSALS": {"EDIT_PROPOSAL": "Proposal successfully updated", "REGISTER_PROPOSAL": "New proposal was added", "REGISTER_PROPOSAL_NO_EMPLOYEE_SELECTED": "Please select an employee from the dropdown menu.", "REGISTER_PROPOSAL_ERROR": "{{ error }}", "DELETE_PROPOSAL": "Proposal was removed", "PROPOSAL_ACCEPTED": "Proposal status updated to Accepted", "PROPOSAL_SENT": "Proposal status updated to Sen<PERSON>"}, "POLICY": {"ADD_POLICY": "Time off policy '{{ name }}' was added", "EDIT_POLICY": "Time off policy '{{ name }}' was changed", "DELETE_POLICY": "Time off policy '{{ name }}' was removed", "ERROR": "{{ error }}", "SAVE_ERROR": "Unable to create Policy record", "POLICY_EXISTS": "Time off policy '{{ name }}' already exists"}, "USER": {"EDIT_PROFILE": "Your profile has been updated successfully."}, "CANDIDATE": {"INVALID_FORM": "Please fill the form", "INVALID_FEEDBACK_INFO": "Please add feedback information", "EXPERIENCE": {"INVALID_CANDIDATE_NAME": "Please add a Skill name", "INVALID_FORM": "Please fill the form", "INVALID_FIELD": "Please fill the field", "ERROR": "{{ error }}"}}, "EMPLOYEE": {"EDIT_EMPLOYEE_AWARDS": {"ADD_AWARD": "New award '{{ name }}' was added", "INVALID_AWARD_NAME_YEAR": "Please check the Name and Year for Award input", "REMOVE_AWARD": "Award '{{ name }}' was removed"}, "END_WORK": {"DATE_CONFLICT": "The end date ({{ endDate }}) cannot be earlier than the start date ({{ startDate }})."}}, "ORGANIZATIONS": {"ADD_NEW_ORGANIZATION": "Organization '{{ name }}' was added", "DELETE_ORGANIZATION": "Organization '{{ name }}' was removed", "ADD_NEW_USER_TO_ORGANIZATION": "'{{ username }}' was added to '{{ orgname }}'", "DELETE_USER_FROM_ORGANIZATION": "'{{ username }}' set as inactive.", "DATA_ERROR": "{{ error }}", "EDIT_ORGANIZATIONS_PROJECTS": {"ADD_PROJECT": "Project '{{ name }}' was saved", "REMOVE_PROJECT": "Project '{{ name }}' was removed", "INVALID_PROJECT_NAME": "Please fill in the name of your project", "VISIBILITY": "Now project is {{ name }} ", "SYNC_REPOSITORY": "Repository '{{ repository }}' sync with '{{ project }}'", "AUTO_SYNC_SETTING": "Auto Sync Setting for Project '{{ project }}' was saved"}, "EDIT_ORGANIZATIONS_TEAM": {"ADD_NEW_TEAM": "Team '{{ name }}' was added", "EDIT_EXISTING_TEAM": "Team '{{ name }}' was changed", "INVALID_TEAM_NAME": "Please add a Team name and at least one member", "REMOVE_TEAM": "Team '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_EMPLOYEE_LEVELS": {"ADD_EMPLOYEE_LEVEL": "Employee Level '{{ name }}' was added", "REMOVE_EMPLOYEE_LEVEL": "Employee Level '{{ name }}' was removed", "INVALID_EMPLOYEE_LEVEL": "Please add an Employee Level", "ALREADY_EXISTS": "This Employee Level already exists."}, "EDIT_ORGANIZATIONS_VENDOR": {"ADD_VENDOR": "vendor '{{ name }}' was added", "UPDATE_VENDOR": "Vendor '{{ name }}' was changed", "REMOVE_VENDOR": "Vendor '{{ name }}' was removed", "INVALID_VENDOR_NAME": "Please add a <PERSON><PERSON><PERSON> name"}, "EDIT_ORGANIZATIONS_EXPENSE_CATEGORIES": {"ADD_EXPENSE_CATEGORY": "Category '{{ name }}' was added", "UPDATE_EXPENSE_CATEGORY": "Category '{{ name }}' was changed", "REMOVE_EXPENSE_CATEGORY": "Category '{{ name }}' was removed", "INVALID_EXPENSE_CATEGORY_NAME": "Please add a Category name"}, "EDIT_ORGANIZATIONS_POSITIONS": {"ADD_POSITION": "Position '{{ name }}' was added", "UPDATED_POSITION": "Position '{{ name }}' was changed", "REMOVE_POSITION": "Position '{{ name }}' was removed", "INVALID_POSITION_NAME": "Please add a Position name", "ALREADY_EXISTS": "This Position already exists."}, "EDIT_ORGANIZATIONS_DEPARTMENTS": {"ADD_DEPARTMENT": "Department '{{ name }}' was saved", "REMOVE_DEPARTMENT": "Department '{{ name }}' was removed", "INVALID_DEPARTMENT_NAME": "Please add a Department name", "ALREADY_EXISTS": "This Department already exists."}, "EDIT_ORGANIZATIONS_CLIENTS": {"ADD_CLIENT": "New client '{{ name }}' successfully added!", "REMOVE_CLIENT": "Client '{{ name }}' successfully removed!", "INVALID_CLIENT_DATA": "Please check the Name, Primary Email and Primary Phone of your client", "INVITE_CLIENT": "Invitation email sent to '{{ name }}'.", "INVITE_CLIENT_ERROR": "Some error occurred while trying to invite client.", "EMAIL_EXISTS": "This client email already exists as a user"}, "EDIT_ORGANIZATIONS_CONTACTS": {"ADD_CONTACT": "Contact '{{ name }}' was added", "UPDATE_CONTACT": "Contact '{{ name }}' was changed", "REMOVE_CONTACT": "Contact '{{ name }}' was removed", "INVALID_CONTACT_DATA": "Please check the Name, Primary Email and Primary Phone of your contact", "INVITE_CONTACT": "Invitation email sent to '{{ name }}'.", "INVITE_CONTACT_ERROR": "Some error occurred while trying to invite contact.", "EMAIL_EXISTS": "This contact email already exists as a user"}, "EDIT_ORGANIZATIONS_EMPLOYMENT_TYPES": {"ADD_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was added", "UPDATE_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was changed", "INVALID_EMPLOYMENT_TYPE": "Please check the Name for Employment type input", "DELETE_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was removed", "ALREADY_EXISTS": "This employment type already exists."}, "EDIT_ORGANIZATIONS_RECURRING_EXPENSES": {"ADD_RECURRING_EXPENSE": "Recurring expense was added for `{{ name }}`", "UPDATE_RECURRING_EXPENSE": "Recurring expense was changed for '{{ name }}'", "DELETE_RECURRING_EXPENSE": "Recurring expense was removed"}, "EDIT_ORGANIZATIONS_AWARDS": {"ADD_AWARD": "New award '{{ name }}' was added", "INVALID_AWARD_NAME_YEAR": "Please check the Name and Year for Award input", "REMOVE_AWARD": "Award '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_LANGUAGES": {"ADD_LANGUAGE": "New language '{{ name }}' was added", "INVALID_LANGUAGE_NAME_LEVEL": "Please check the Name and Level for Language input", "REMOVE_LANGUAGE": "Language '{{ name }}' was removed"}, "EDIT_ORGANIZATION_DOCS": {"CREATED": "Document '{{ name }}' was added", "ERR_CREATE": "Unable to create new organization document", "ERR_LOAD": "Unable to load organization documents", "UPDATED": "Document '{{ name }}' was changed", "ERR_UPDATED": "Unable to update the selected document!", "SELECTED_DOC": "selected document", "DELETED": "Document '{{ name }}' was removed", "ERR_DELETED": "Unable to delete the selected document!"}}, "DANGER_ZONE": {"WRONG_INPUT_DATA": "Wrong input! Please try again.", "ACCOUNT_DELETED": "Your account was deleted successfully!", "ALL_DATA_DELETED": "Your data was deleted successfully!", "RECORD_TYPE": "If Yes, please type '{{ type }}' to confirm.", "TITLES": {"ACCOUNT": "REMOVE ACCOUNT", "ALL_DATA": "REMOVE ALL DATA"}}, "EVENT_TYPES": {"ADD_EVENT_TYPE": "Event type '{{ name }}' was added", "EDIT_EVENT_TYPE": "Event type '{{ name }}' was changed", "DELETE_EVENT_TYPE": "Event type '{{ name }}' was removed", "ERROR": "{{ error }}"}, "AVAILABILITY_SLOTS": {"SAVE": "Availability Slots saved", "ERROR": "{{ error }}"}}, "TIMER_TRACKER": {"IS_BILLABLE": "Is Billable", "STOP_TIMER": "Stop Timer", "START_TIMER": "Start Timer", "TIMER": "Timer", "MANUAL": "Manual", "MANUAL_NOT_ALLOW": "Manual time not allowed", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_CLIENT": "Select Client", "SELECT_TEAM": "Select Team", "DATE": "Date", "START_TIME": "Start Time", "END_TIME": "End Time", "DESCRIPTION": "Description", "ADD_TIME_SUCCESS": "Time was added", "VALIDATION": {"CLIENT_REQUIRED": "Please select client", "PROJECT_REQUIRED": "Please select project", "TASK_REQUIRED": "Please select task", "DESCRIPTION_REQUIRED": "Description is required"}, "VIEW_TIMESHEET": "View timesheet", "ADD_TIME": "Add Time", "TODAY": "Today", "ALERT_DESKTOP_DOWNLOAD": "For more flexible time and activity tracking, please <a href={{downloadURL}} rel=\"noopener\" target=\"_blank\">Download</a> and use the <a href={{downloadURL}} rel=\"noopener\" target=\"_blank\">Gauzy Desktop Timer App</a>.", "STATUS": "The time tracker is already running in the {{ source }}", "VERSION": "Version v{{ version }}", "WAIT_FOR": "Waiting for {{ name }} to start...", "SETUP": {"WELCOME": "Welcome", "TITLE": "Welcome to Ever® Gauzy™ - Open-Source Business Management Platform (ERP/CRM/HRM)", "LABEL": "Ever Gauzy Desktop App provides the full functionality of the Gauzy Platform available directly on your desktop computer or a laptop. In addition, it allows tracking work time, activity recording, and the ability to receive tracking reminders/notifications.", "TITLE_SERVER": "Ever Gauzy Server Installation Wizard", "LABEL_SERVER": "Ever Gauzy Desktop App provides the full functionality of the Gauzy Platform available directly on your desktop computer or a laptop. In addition, it allows tracking work time, activity recording, and the ability to receive tracking reminders/notifications.", "FUNCTIONALITIES": "Functionalities", "WHAT_WOULD_LIKE_USE": "What would you like to use?", "SELECT_MULTIPLE_OPTIONS": "You can select only one option or both options.", "TIME_ACTIVITY_FEATURES": "Time & Activity Tracking Features", "GP_FEATURES": "Gauzy Platform Features", "HOW_GONNA_USE_GP": "How are you going to use Gauzy platform?", "SELECT_ONE_OPTION": "You have to select only one of the options below.", "INTEGRATED": "Integrated", "INSTALL_GP_LOCAL": "Install and run Gauzy Platform on your local computer.", "CUSTOM_NETWORK": "Custom / Network", "SEPARATE_SERVER": "Connect to a separate server in your work network.", "LIVE": "Live", "CONNECTED_LIVE_SERVER": "Connected to our live server and use it as SAAS.", "GONNA_USE_3RD_PARTY": "Are you going to need 3rd party integrations?", "SELECT_MULTIPLE_OPTIONS_3RD_PARTY": "This is an option, you can select one or both type of 3rd party integrations.", "LEGAL_NOTE": "Legal: All product names, brand names, logos, trademarks, and registered trademarks are the property of their respective owners. We have no affiliation with or endorsement, sponsorship, or support from mentioned third-party companies, brands, products, sites, or services. The links to third-party software are being provided as a convenience and for informational purposes only; they do not constitute an endorsement or approval of any of the products, services, or opinions of the corporation or organization or individual. We use respective logos and brand names only to advertise that our product use corresponding products & technologies or has built-in integration with corresponding services.", "SETTING": "Setting", "READY_FOR_ADVANCED_SETTINGS": "Are you ready for some advanced settings?", "FINAL_STEP_GP": "This is the final step where you can get to the nitty-gritty of your Gauzy Platform.", "PORT": "Port", "PORT_API": "Port API", "UI": "Ui", "API_HOST": "API Host", "HOST": "Host", "DB_NAME": "Database Name", "DB_PORT": "Database Port", "USER": "User", "PASSWORD": "Password", "UI_HOSTNAME": "UI Hostname / IP Address", "TITLE_SERVER_API": "Ever Gauzy API Server Installation Wizard", "LABEL_SERVER_API": "Ever Gauzy API Server Desktop App provides the full functionality of the Gauzy Platform available directly on your desktop computer or a laptop. In addition, it allows tracking work time, activity recording, and the ability to receive tracking reminders/notifications.", "UNABLE_TO_CONNECT": "Unable to connect to the server. Please check your API host or internet connection."}, "SETTINGS": {"SCREEN_CAPTURE": "Screen Capture", "MONITOR": "Monitor", "AUTOMATIC_SCREEN_CAPTURE": "Automatic Screen Capture", "NOTIFICATION_SETTINGS": "Notifications Settings", "DESKTOP_NOTIFICATIONS": "Desktop Notifications", "SHOW_DESKTOP_NOTIF_SCREEN_CAPTURE": "Show desktop notification on screen capture", "NOTIFICATION": "Notification", "SIMPLE_NOTIF": "Simple Notification", "DETAILED_NOTIF": "Detailed Notification", "SHOW_NOTIF_CAPTURED_IMG": "Show notification with captured image", "SHOW_NATIVE_OS_NOTIF": "Show native OS notification", "SOUND_NOTIF": "Sound Notifications", "PLAY_SOUND": "Play sound on screen capture", "SOUND_ENABLED": "Sound Enabled", "SOUND_DISABLED": "Sound Disabled", "UPDATE_ACTIVITIES": "Update activities or screen capture every", "RANDOM_SCREENSHOT_TIME": "Random Screenshot Time", "TRACK_TIME_PC_LOCKED": "Track Time When PC Locked", "KEEP_SYSTEM_ACTIVE": "Keeps system and screen active", "PREVENT_DISPLAY_GOING_SLEEP": "Prevent the display from going to sleep", "PREVENT_DISPLAY_SLEEP": "Prevent display sleep", "UPDATE": "Update", "AUTOMATIC_UPDATE_CHECK": "Automatic Update Check", "ENABLE_AUTOMATIC_UPDATE_LABEL": "Enable automatic update check, in order to run a request to check if a new version is available and notify", "SET_UPDATE_INTERVAL_DURATION": "Set update check interval duration", "SELECT_DELAY": "Select Delay", "ENABLE_AUTOMATIC_UPDATE": "Enable Automatic Update", "UPDATE_SERVER": "Updater Server", "SELECT_DEFAULT_CDN": "Select default CDN server for update", "TOGGLE_UPDATE_LOCALLY": "Toggle to update locally", "LOCAL_SERVER": "Local Server", "LOCAL_SERVER_NOTE": "Directory selected should contains definitely the <strong>latest.yml</strong> and/or <strong>latest-mac.yml</strong> manifest in order to make update validation", "OTHER_SETTINGS": "Other Settings", "ALLOW_PRERELEASE_VERSIONS": "Allow prerelease versions", "CHECK_UPDATE_APP_VERSION": "Check & update your app version", "UPDATE_DOWNLOADED_NOTE": "New Update is already downloaded! Please click button Update Now below.", "UPDATE_AVAILABLE_NOTE": "New Update is available! Please click button Download Now below.", "CHECK_UPDATE_NOTE": "You can check for updates by clicking the Check Update's button below.", "UPDATE_LOGS": "Update Logs", "ADVANCED_SETTINGS": "Advanced Settings", "WARNING_STOP_TIMER": "Please Stop Timer if you want to change config", "GENERAL": "General", "API_CONFIG": "API Configuration", "SERVER_ACCESS_CONFIG": "Server Access Configuration", "API_SERVER_PORT": "API Server Port", "UI_SERVER_PORT": "UI Server Port", "SERVER_HOSTNAME": "Server Hostname/ IP Address", "AUTO_START_STARTUP": "Auto Start On Startup", "SERVER_TYPE": "Server Type", "LOCAL_API_PORT": "Local API Port", "UI_PORT": "UI Port", "SERVER_URL": "Server URL", "DB_CONFIG": "Database Configuration", "DB_DRIVER": "Database Driver", "STARTUP_CONFIG": "Startup Configuration", "AUTOMATIC_LAUNCH": "Automatic Launch", "MIN_ON_STARTUP": "Minimize On Startup", "3RD_PARTY": "Third Party", "3RD_PARTY_CONFIG": "Third Party Configuration", "AW_PORT": "Activity Watch Port", "VISIBLE_AW": "Visible Activity Watch option on window", "VISIBLE_WAKATIME": "Visible Wakatime option on window", "SIGN_IN_AS": "Signed in as {{ name }} ({{ email }})", "SIGN_OUT": "Sign Out", "DB_PASSWORD": "Database Password", "DB_USERNAME": "Database UserName", "DB_HOST": "Database Host", "CAPTURE_ALL_MONITORS": "Capture All Monitors", "ALL_CONNECTED_MONITORS": "All connected monitors", "MONITOR_CURRENT_POSITION": "Monitor current pointer position", "CAPTURE_ACTIVE_MONITOR": "Capture Active Monitor", "MESSAGES": {"APP_UPDATE": "Application Update", "UPDATE_NOT_AVAILABLE": "Update Not Available", "UPDATE_ERROR": "Update Error", "UPDATE_AVAILABLE": "Update Available", "UPDATE_DOWNLOAD_COMPLETED": "Update Download Completed", "UPDATE_DOWNLOADING": "Update Downloading", "SERVER_CONFIG_UPDATED": "Server configuration updated, please wait till server restarts", "SERVER_RESTARTED": "Server Restarted Successfully", "CONNECTION_SUCCEEDS": "Connection to Server {{ url }} Succeeds", "DOWNLOADING_UPDATE": "Downloading update {{ current }} MB of {{ total }} MB ->> {{ bandwidth }} KB/s"}, "TIMEZONE": "Time Zone", "TIMEZONE_LABEL": "Choosing 'Local' displays time in your local time zone, while selecting 'UTC' shows time in the universal UTC timezone used by billing and other systems.", "TIMEZONE_PLACEHOLDER": "Select either 'Local' or 'UTC'.", "TIMEZONE_LOCAL": "Local", "TIMEZONE_UTC": "UTC", "WIDGET": "Widget", "WIDGET_LABEL": "This window reflect the status of the timer, indicating whether it is running or stopped, and function as a button.", "SSL": {"SSL_CONFIG": "SSL Configuration", "USE_SSL": "Use SSL", "USE_SSL_INFO": "Turns on SSL Encryption.", "SSL_KEY_FILE": "SSL Key File", "SSL_KEY_FILE_INFO": "Path to Client Key file for SSL.", "SSL_CERT_FILE": "SSL Cert File", "SSL_CERT_FILE_INFO": "Path to Client Certificate file for SSL.", "SECURE": "Secure", "SECURE_INFO": "True, if you want to verify the SSL Certs."}, "PLUGINS": "Plugins", "PLUGIN_DESCRIPTION": "Plugins", "PLUGIN": "Plugin", "VERSION": "Version", "ADD_PLUGIN_INSTALLATION_DESCRIPTION": "Install plugins from local files or CDN. Local for custom/private plugins, CDN for public ones. Choose based on your needs.", "ADD_PLUGIN_CDN_DESCRIPTION": "CDN installation allows for easy access to publicly available plugins.", "ADD_PLUGIN_NPM_DESCRIPTION": "NPM installation allows for access to publicly and private available plugins.", "PACKAGE_NAME": "NPM Package Name", "PACKAGE_VERSION": "NPM Package Version", "REGISTRY": "NPM Registry", "AUTH_TOKEN": "Authentication Token", "PRIVATE_REGISTRY_URL": "Private Registry URL", "PLUGIN_INSTALL_CDN_ERROR": "The server URL mustn't be empty.", "TRACK_KEYBOARD_MOUSE_ACTIVITY": "Track Keyboard & Mouse Activity"}, "WEEKLY_LIMIT_EXCEEDED": "Weekly limit exceeded", "OF_HRS": "Of {{ limit }} hrs", "TIME_TRACKER_DISABLED": "Time tracker has been disabled", "STOP_TIMER_CHANGE_CLIENT": "Please stop the timer before changing the client", "ADD_CONTACT": "Add contact", "ADD_TASK": "Add Task", "STOP_TIMER_CHANGE_PROJECT": "Please stop the timer before changing the project", "STOP_TIMER_CHANGE_TASK": "Please stop the timer before changing the task", "STOP_TIMER_CHANGE_DESCRIPTION": "Please stop the timer before changing the description", "STOP_TIMER_CHANGE_TEAM": "Please stop the timer before changing the team", "ACTIVITY_WATCH_INTEGRATION": "ActivityWatch Integration", "WAKATIME_INTEGRATION": "Wakatime Integration", "LAST_CAPTURE_TAKEN": "Last screen capture taken", "REQUEST_TASK_PERMISSION": "Request permission from the organization administrator", "RETURN_ONLINE": "Return Online", "SEARCH": "Search", "OPEN_SETTINGS": "Open Settings", "REFRESH": "Refresh", "SWITCHED_OFFLINE": "You switched to offline mode now", "SWITCHED_ONLINE": "You switched to online mode now", "ONLINE": "Online", "OFFLINE": "Offline", "SYNCED": "Synced", "SYNCED_PROGRESS": "Synced in progress", "WAIT_SYNCED": "Waiting for synced", "DIALOG": {"WARNING": "Warning", "REMOVE_SCREENSHOT": "Do you really want to remove this screenshot and activities log?", "CHANGE_CLIENT": "Are you sure you want to change <PERSON><PERSON>?", "RESUME_TIMER": "Your timer was running when <PERSON> was locked. Resume timer?", "EXIT": "Are you sure you want to exit?", "STOPPED_DU_INACTIVITY": "Timer was stopped due to inactivity period exceeding {{ inactivity }}. Please make sure you start timer again when continue working.", "EXIT_CONFIRM": "Click Exit to Stop the Timer and Exit from the application.", "EXIT_SERVER_CONFIRM": "Click Exit to Stop the Server and Exit from the application.", "LOGOUT_CONFIRM": "<PERSON>lick Logout to Stop the Timer and Logout from the application.", "SELECT_UPDATE_FILES": "Please select folder with update files.", "UPDATE_READY": "Update Ready to Download", "NEW_VERSION_AVAILABLE": "A new version v{{ next }} is available. Upgrade the application by downloading the updates for v{{ current }}", "READY_INSTALL": "Update Ready to Install", "HAS_BEEN_DOWNLOADED": "A new version v{{ version }} has been downloaded. Restart the application to apply the updates.", "CONNECTION_DRIVER": "Connection to {{ driver }} DB Succeeds", "STILL_WORKING": "Are you still working?", "INACTIVITY_HANDLER": "Inactivity Handler", "WANT_LOGOUT": "Are you sure you want to logout?", "ERROR_HANDLER": "<PERSON><PERSON><PERSON>", "ERROR_OCCURRED": "<PERSON><PERSON><PERSON>urred", "SELECT_FILE": "Select file", "EXIT_AGENT_CONFIRM": "Oops! You need to log in before you can use the app."}, "NO_LIMIT": "∞ No Limit", "TASK": "Task", "DUE": "Due", "AW_CONNECTED": "ActivityWatch's connected", "AW_DISCONNECTED": "ActivityWatch's disconnected", "TOASTR": {"REMOVE_SCREENSHOT": "Successfully remove last screenshot and activities", "CANT_RUN_TIMER": "Your can't run timer for the moment", "NOT_AUTHORIZED": "Your are not authorized to work", "ACCOUNT_DELETED": "Your account is already deleted", "PROJECT_ADDED": "Project added successfully", "TASK_ADDED": "Task added successfully", "CLIENT_ADDED": "Client added successfully"}, "NATIVE_NOTIFICATION": {"STOPPED_DU_INACTIVITY": "Tracker was stopped due to inactivity!", "SCREENSHOT_TAKEN": "Screenshot taken", "SCREENSHOT_REMOVED": "Successfully remove last screenshot and activities", "NEW_VERSION_AVAILABLE": "New update for {{ name }} (version {{ version }}) is available"}, "MENU": {"ZOOM_IN": "Zoom In", "ZOOM_OUT": "Zoom Out", "SETTING_DEV_MODE": "Setting Dev Mode", "SERVER_DEV_MODE": "Server Dashboard Developer Mode", "TIMER_DEV_MODE": "Timer Dev Mode", "NOW_TRACKING": "Now tracking time - {{ time }}", "START_TRACKING": "Start Tracking Time", "STOP_TRACKING": "Stop Tracking Time", "OPEN_TIMER": "Open Time Tracker", "WINDOW": "Window", "HELP": "Help", "LEARN_MORE": "Learn More", "OPEN_MAIN_WINDOW": "Open Main Window", "DAILY_RECAP": "Daily Recap", "WEEKLY_RECAP": "Weekly Recap", "MONTHLY_RECAP": "Monthly Recap", "INSTALL_PLUGIN": "Install Plugin"}, "RECAP": {"HOURLY_TIME_TRACKING": "Hourly Time Tracking", "HOURLY_TIME_TRACKING_DATA": "Hourly Time Tracking Data", "WORKED_FOR_MONTH": "Worked this month", "ACTIVITY_FOR_MONTH": "Monthly Activity", "NO_MONTHLY_ACTIVITY": "You have not any tracked time and activity for this month."}, "LOADING": {"PLEASE_HOLD": "Please hold...", "SHUTTING_DOWN": "Application is shutting down", "LOGOUT_IN_PROGRESS": "Logout in progress"}}, "SERVER": "Server", "SERVER_LOG": "Server Log", "TIMESHEET": {"TODAY": "Today", "DAILY": "Daily", "WEEKLY": "Weekly", "WEEK": "Week", "MONTHLY": "Monthly", "CALENDAR": "Calendar", "APPROVALS": "Approvals", "APPROVE_SUCCESS": "Timesheet Successfully Approved", "DENIED_SUCCESS": "Timesheet Successfully Denied", "SUBMIT_SUCCESS": "Timesheet Successfully Submitted", "UNSUBMIT_SUCCESS": "Timesheet Successfully Unsubmit", "DELETE_TIMELOG": "Are you sure you want to delete Timelog?", "SELECT_EMPLOYEE": "Select Employee", "ALL_EMPLOYEE": "All Employees", "SELECT_SOURCE": "Select Source", "SELECT_ACTIVITY_LEVEL": "Select Activity Level", "SELECT_LOG_TYPE": "Select Log Type", "ADD_TIME_LOGS": "Add Time Logs", "EDIT_TIME_LOGS": "Edit Time Logs", "VIEW_TIME_LOGS": "Time Logs", "ADD_TIME": "Add Time", "UPDATE_TIME": "Update Time", "TILL_NOW": "Till now", "VIEW": "View", "EDIT": "Edit", "CLOSE": "Close", "DELETE": "Delete", "IMMUTABLE_TIME": "Immutable Time", "BULK_ACTION": "Bulk action", "LOG_TYPE": "Log type", "SOURCE": "Source", "TOTAL_TIME": "Total Time", "ACTIVITIES": "Activities", "APPROVED_AT": "Approved At", "SUBMITTED_AT": "Submitted At", "STATUS": "Status", "SUBMIT_TIMESHEET": "Submit Timesheet", "UNSUBMIT_TIMESHEET": "Unsubmit Timesheet", "APPROVE": "Approve", "DENY": "<PERSON><PERSON>", "TIME_SPAN": "Time span", "ACTION": "Action", "EMPLOYEE": "Employee", "DURATION": "Duration", "ORGANIZATION_CONTACT": "Client", "NO_ORGANIZATION_CONTACT": "No Client", "PROJECT": "Project", "NO_PROJECT": "No Project", "NO_TIMELOG": "No timelogs found", "TODO": "To-do", "NO_TODO": "No To-do", "OVERLAP_MESSAGE": "This time entry overlaps activity in other projects/tasks, Which will be replaced:", "REASON": "Reason", "NOTES": "Notes", "APPS": "Apps", "TIME_OVERLAPS": "Time overlaps", "TIME_TRACKING": "Time Tracking", "MEMBERS_WORKED": "Members worked", "PROJECTS_WORKED": "Projects worked", "ACTIVITY_OVER_PERIOD": "Activity over the period", "ACTIVITY_FOR_DAY": "Activity for the day", "ACTIVITY_FOR_WEEK": "Weekly Activity", "WORKED_THIS_WEEK": "Worked this week", "WORKED_OVER_PERIOD": "Worked over the period", "WORKED_FOR_DAY": "Worked for the day", "WORKED_FOR_WEEK": "Worked for the week", "TODAY_ACTIVITY": "Today's Activity", "WORKED_TODAY": "Worked today", "RECENT_ACTIVITIES": "Recent Activities", "NO_SCREENSHOT_DAY": "No screenshot for the day", "NO_SCREENSHOT_WEEK": "No screenshot for the week", "NO_SCREENSHOT_PERIOD": "No screenshot for over the period", "TASKS": "Tasks", "NO_TASK_ACTIVITY_DAY": "No task activity for the day", "NO_TASK_ACTIVITY_WEEK": "No task activity for the week", "NO_TASK_ACTIVITY_PERIOD": "No task activity for over the period", "MANUAL_TIME": "Manual Time", "NO_MANUAL_TIME_DAY": "No manual time for the day", "NO_MANUAL_TIME_WEEK": "No manual time for the week", "NO_MANUAL_TIME_PERIOD": "No manual time for over the period", "PROJECTS": "Projects", "NO_PROJECT_ACTIVITY_DAY": "No project activity for the day", "NO_PROJECT_ACTIVITY_WEEK": "No project activity for the week", "NO_PROJECT_ACTIVITY_PERIOD": "No project activity for the period", "APPS_URLS": "Apps & Urls", "NO_APP_URL_ACTIVITY_DAY": "No app & url activity for the day", "NO_APP_URL_ACTIVITY_WEEK": "No app & url activity for the week", "NO_APP_URL_ACTIVITY_PERIOD": "No app & url activity for over the period", "DATE": "Date", "MEMBERS": "Members", "MEMBER": "Member", "MEMBER_INFO": "Member info", "THIS_WEEK": "This week", "OVER_PERIOD": "Over the period", "NO_MEMBER_ACTIVITY_DAY": "No member activity for the day", "NO_MEMBER_ACTIVITY_WEEK": "No member activity for the week", "NO_MEMBER_ACTIVITY_PERIOD": "No member activity for over the period", "TITLE": "Title", "URL": "Url", "TIME_SPENT": "Time spent (hours)", "ACTIVITY_LEVEL": "Activity Level", "VALIDATION": {"DESCRIPTION": "Description is required", "TASK": "Please select task", "PROJECT": "Please select project", "EMPLOYEE": "Please select employee", "REASON": "Please select reason"}, "SCREENSHOTS": {"SCREENSHOTS": "Screenshots", "OF": "of", "TIME_LOG": "TimeLog"}, "RUNNING_TIMER_WARNING": "Warning. Timer is running, you can only remove screenshots if you stop timer or Inside Timer app", "DELETE_CONFIRM": "Are you sure to delete this screenshots and time log?", "VIEW_LOG": "View Log", "NO_DATA": {"DAILY_TIMESHEET": "You have not any time log yet for this day.", "WEEKLY_TIMESHEET": "You have not any time log yet for these day's.", "APPROVAL_TIMESHEET": "You don't have any timesheets for approvals yet for these day's."}, "VIEW_WINDOWS": "View windows", "VIEW_WIDGETS": "View widgets", "SOURCES": {"WEB_TIMER": "Web Timer", "DESKTOP": "Desktop Timer App", "MOBILE": "Mobile Timer App", "UPWORK": "Upwork", "HUBSTAFF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BROWSER_EXTENSION": "Browser Extension", "TEAMS": "Teams", "CLOC": "Cloc"}, "LAST_WORKED": "Last worked:", "TIME_ZONE": "Time Zone", "TIME_FORMAT": "Time Format", "TIME_ZONE_OPTION": {"UTC": "UTC", "ORG_TIMEZONE": "Org Timezone", "MY_TIMEZONE": "My Timezone"}}, "ACTIVITY": {"SCREENSHOTS": "Screenshots", "PERCENT_USED": "Percent used", "TIME_SPENT": "Time spent (hours)", "APPS": "Apps", "VISITED_SITES": "Visited Sites", "NO_ACTIVITIES": "No records found. Please select date range, employee or project.", "NO_ACTIVITY": "No Activity", "TIME_AND_ACTIVITIES": "Time & Activities", "VIEW_SCREEN": "View Screen", "VIEW_INFO": "View Info", "MINUTES": "Minutes", "NO_EMPLOYEES_SELECTED": "Please select Employee", "DELETE_CONFIRM": "Are you sure you want to delete this screenshots and activities?", "VISITED_DATES": "Visited Dates", "NO_SCREENSHOT": "No Screenshot", "NO_SCREENSHOTS": "No Screenshots", "NO_RECORD_FOUND": "No records found. Please select date range, employee or project."}, "ONBOARDING": {"FIRST_ORGANIZATION": "Let's create your first organization", "COMPLETE_TITLE": "You're all set!", "COMPLETE_SUB_TITLE": "Where would you like to go now?"}, "VALIDATION": {"FIELD_REQUIRED": "This field is required!", "ENTER_POSITIVE_NUMBER": "Please enter positive number!"}, "APPOINTMENTS_PAGE": {"ADD_APPOINTMENT": "Add Appointment", "EDIT_APPOINTMENT": "Edit Appointment", "SAVE_SUCCESS": "Appointment was saved", "SAVE_FAILED": "Failed to save appointment", "CANCEL_APPOINTMENT": "<PERSON>cel Appointment", "CANCEL_FAIL": "Failed to cancel appointment", "CANCEL_SUCCESS": "Appointment was cancelled", "DURATION_ERROR": "Selected Duration is not valid", "SELECT_EMPLOYEE": "Select Employee", "EMPLOYEE": "Employee", "BUFFER_TIME": "Buffer Time", "BUFFER_AT_START": "Buffer at Start", "BUFFER_AT_END": "Buffer at End", "BREAK_TIME": "Break Time", "ARE_YOU_SURE": "Are you sure? This action is irreversible."}, "EMPLOYEE_SCHEDULES_MODAL": {"EMPLOYEE": "Employee", "SLOTS_AVAILABLE": "Employee is available only for the below time slots, Do you still want to continue?", "SLOTS_UNAVAILABLE": "Employee unavailable for this time slot"}, "EVENT_TYPE_PAGE": {"EVENT_TYPE": "Event Types", "MANAGE_EVENT_TYPE": "Manage Event Type", "EVENT_NAME": "Event Type Name", "EVENT_DURATION": "Duration", "EVENT_DESCRIPTION": "Description", "ACTIVE": "Active", "EMPLOYEE": "Employee", "YES": "Yes", "NO": "No", "DURATION_UNIT": "{{ unit }}"}, "SCHEDULE": {"DATE_SPECIFIC_AVAILABILITY": "Date Specific Availability", "SELECT_EMPLOYEE": "Please select employee from the menu above.", "RECURRING_AVAILABILITY": "Recurring Availability", "DATE_SPECIFIC_AVAILABILITY_TOOLTIP": "You can use Date specific availability to mark availability for a particular date or add exceptions to your Recurring availability pattern. The changes you make will override your Weekly recurring availability on those specific dates only.", "RECURRING_AVAILABILITY_TOOLTIP": "You can use Recurring availability to mark your availability on a weekly basis. To change your availability for specific days only, you must use Date-specific availability.", "MONDAY_FRIDAY": "Monday - Friday", "SUNDAY_THURSDAY": "Sunday - Thursday"}, "PUBLIC_APPOINTMENTS": {"BOOK_APPOINTMENT": "Book Appointment", "BOOK_APPOINTMENTS": "Book an appointment", "SELECT_EVENT_TYPES": "Please select an event type", "PICK_DATETIME": "Pick a date and time", "DURATION": "Duration:", "EVENT_TYPE": "Event Type:", "CONFIRM_APPOINTMENT": "Appointment Confirmed", "APPOINTMENT_INFO": "Appointment Information", "DETAILS": "Booking Details", "PARTICIPANTS": "Participant Emails", "HOST": "Host", "RESCHEDULE": "Reschedule Appointment", "CANCEL": "<PERSON>cel Appointment", "TIMEZONE": "Your time zone:", "CHANGE": "(Change)", "SELECT_EMPLOYEE_ERROR": "Please select an employee.", "EXPIRED_OR_CANCELLED": "**This appointment has expired or has been cancelled.", "EMAIL_SENT": "**An email has been sent to the host as well as to all the participants for this meeting.", "NO_ACTIVE_EVENT_TYPES": "There are no active event types", "CLICK_HERE": "Click Here"}, "EMAIL_TEMPLATES_PAGE": {"HEADER": "Email Templates", "SAVE": "Save", "LABELS": {"LANGUAGE": "Language", "TEMPLATE_NAME": "Template Name", "SUBJECT": "Subject", "EMAIL_BODY": "Email Body", "EMAIL_PREVIEW": "Email Preview", "SUBJECT_PREVIEW": "Subject Preview"}, "TEMPLATE_NAMES": {"password": "Password Reset", "password-less-authentication": "Password Less Authentication", "multi-tenant-password": "Multi-Tenant Forgot Password", "welcome-user": "Welcome User", "email-verification": "Email Confirmation", "invite-organization-client": "Invite Organization Client", "email-estimate": "Email Estimate", "email-invoice": "Email Invoice", "invite-employee": "Invite Employee", "invite-user": "Invite User", "invite-gauzy-teams": "Invite Gauzy <PERSON>", "appointment-confirmation": "Appointment Confirmation", "appointment-cancellation": "Appointment Cancellation", "equipment": "Equipment Create", "equipment-request": "Equipment Request", "timesheet-overview": "Time sheet overview", "timesheet-submit": "Time Sheet Submit", "timesheet-action": "Time Sheet Actions", "timesheet-delete": "Time Sheet Delete", "time-off-report-action": "Time off policy action", "task-update": "Task Update", "candidate-schedule-interview": "Candidate Interview Schedule", "interviewer-interview-schedule": "Interviewer Schedule", "employee-join": "Employee Join", "email-reset": "<PERSON><PERSON>", "organization-team-join-request": "Organization team join request", "payment-receipt": "Payment Receipt", "reject-candidate": "Reject Candidate"}, "HTML_EDITOR": "HTML Editor"}, "PIPELINES_PAGE": {"HEADER": "Pipelines", "HEADER_STAGES": "Stages", "VIEW_DEALS": "View Deals", "HEADER_FORM_EDIT": "<PERSON>", "HEADER_FORM_CREATE": "Create Pipeline", "RECORD_TYPE": "pipeline \"{{ name }}\"", "ACTIVE": "Active", "INACTIVE": "Inactive", "BROWSE": "Browse", "SEARCH": "Search", "SEARCH_PIPELINE": "Search Pipeline", "NAME": "Name", "STAGE": "Stage", "SEARCH_STAGE": "Search Stage", "STATUS": "Status", "ALL_STATUS": "All Status", "RESET": "Reset"}, "PIPELINE_DEALS_PAGE": {"HEADER": "Pipeline Deals", "FILTER_BY_STAGE": "Filter by Stage", "RECORD_TYPE": "stage \"{{ title }}\"", "ALL_STAGES": "All Stages", "LOW": "Low", "MEDIUM": "Medium", "HIGH": "High", "UNKNOWN": "Unknown", "DEAL_DELETED": "Deal '{{ name }}' was removed", "DEAL_EDITED": "Deal '{{ name }}' was changed", "DEAL_ADDED": "Deal '{{ name }}' was added"}, "PIPELINE_DEAL_EDIT_PAGE": {"HEADER": "Edit Deal | Pipeline: '{{ name }}'"}, "PIPELINE_DEAL_CREATE_PAGE": {"HEADER": "Create Deal | Pipeline: '{{ name }}'", "SELECT_STAGE": "Select Stage", "PROBABILITY": "Probability", "SELECT_CLIENT": "Select Client"}, "DIALOG": {"CONFIRM": "Confirm", "ALERT": "<PERSON><PERSON>", "DELETE_CONFIRM": "<PERSON><PERSON>", "QUICK_ACTIONS": "Quick Actions"}, "SETTINGS_FILE_STORAGE": {"FILE_PROVIDER": "File provider", "S3": {"HEADER": "S3 Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket"}}, "WASABI": {"HEADER": "Wasabi Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL", "FORCE_PATH_STYLE": "Force path style URLs"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL"}}, "CLOUDINARY": {"HEADER": "Cloudinary Configuration", "LABELS": {"CLOUD_NAME": "Cloudinary name", "ACCESS_API_KEY": "Access key id", "ACCESS_API_SECRET": "Secret access key", "DELIVERY_URL": "Delivery URL", "SECURE": "Secure"}, "PLACEHOLDERS": {"CLOUD_NAME": "Cloudinary name", "ACCESS_API_KEY": "Access key id", "ACCESS_API_SECRET": "Secret access key", "DELIVERY_URL": "Delivery URL", "SECURE": "Secure"}}, "DIGITALOCEAN": {"HEADER": "DigitalOcean Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL", "CDN_URL": "CDN URL", "FORCE_PATH_STYLE": "Force path style URLs"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL", "CDN_URL": "CDN URL"}}}, "CUSTOM_SMTP_PAGE": {"HEADER": "Manage SMTP for '{{ name }}'", "FROM_ADDRESS": "From Email Address", "HOST": "Host", "PORT": "Port", "SECURE": "Secure", "AUTH": {"USERNAME": "Username", "PASSWORD": "Password"}}, "SMS_GATEWAY_PAGE": {"HEADER": "SMS Provider", "TWILIO": "<PERSON><PERSON><PERSON>"}, "FEATURE_PAGE": {"HEADER": "Manage Features for '{{ name }}'"}, "SETTINGS": {"EMAIL_HISTORY": {"EMAIL_ARCHIVED": "Email Archived", "RESEND": "Resend", "ARCHIVE": "Archive", "HEADER": "Email History", "FROM": "From:", "TO": "To:", "DATE": "Date:", "SUBJECT": "Subject:", "LANGUAGE": "Language:", "TEMPLATE": "Template:", "NO_EMAILS_SENT": "No emails sent.", "SYSTEM": "System", "FILTERS": {"TO": "To", "TEMPLATE_LANGUAGE": "Template/Language"}}}, "GAUZY_MAINTENANCE": "{{ companySite }} @ Maintenance", "LEGAL": {"PRIVACY_POLICY": "Privacy Policy", "TERMS_AND_CONDITIONS": "Terms & Conditions"}, "LOADING": "Loading, please hold....", "ACCOUNTING_TEMPLATES_PAGE": {"HEADER": "Accounting Templates", "TEMPLATE_NAMES": {"invoice": "Invoice", "estimate": "Estimate", "receipt": "Receipt"}}, "REGISTER_PAGE": {"TITLE": "Register", "HAVE_AN_ACCOUNT": "Already have an account?", "LABELS": {"FULL_NAME": "Full name:", "EMAIL": "Email address:", "PASSWORD": "Password", "CONFIRM_PASSWORD": "Confirm Password:"}, "PLACEHOLDERS": {"FULL_NAME": "Full name", "EMAIL": "Email address", "PASSWORD": "Password", "CONFIRM_PASSWORD": "Confirm password"}, "VALIDATIONS": {"FULL_NAME_REQUIRED": "Full name is required!", "FULL_NAME_SHOULD_CONTAIN": "Full name should contain from {{ minLength }} to {{ maxLength }} characters", "EMAIL_REQUIRED": "Email is required!", "EMAIL_SHOULD_BE_REAL": "Email should be a real one!", "PASSWORD_NO_SPACE_EDGES": "Passwords must not begin or end with spaces!", "PASSWORD_REQUIRED": "Password is required!", "PASSWORD_SHOULD_CONTAIN": "Password should contain from {{ minLength }} to {{ maxLength }} characters!", "CONFIRM_PASSWORD_REQUIRED": "Password confirmation is required!", "PASSWORDS_NOT_MATCH": "Password does not match the confirm password!", "CHECK_BOX_TEXTS": {"AGREE_TO": "Agree to", "TERMS_AND_CONDITIONS": "Terms & Conditions", "AND": " and ", "PRIVACY_POLICY": "Privacy Policy"}}}, "LOGIN_PAGE": {"TITLE": "<PERSON><PERSON>", "SUB_TITLE": "Hello! Log in with your email.", "REMEMBER_ME_TITLE": "Remember me", "DO_NOT_HAVE_ACCOUNT_TITLE": "Don't have an account?", "FORGOT_PASSWORD_TITLE": "Forgot Password?", "OR_SIGN_IN_WITH": "Or Sign In with", "FORGOT_EMAIL_TITLE": "Forgot Email?", "LOGIN_MAGIC": {"TITLE": "Log in with a magic code.", "DESCRIPTION_TITLE": "We'll email you a magic code for a password-free sign-in. Or you can ", "OR_SIGN_IN_WITH_PASSWORD": "sign in using password instead.", "RESEND_CODE_TITLE": "Resend code", "SUCCESS_SENT_CODE_TITLE": "A 6-character code sent to: ", "SUCCESS_SENT_CODE_SUB_TITLE": "The code expires shortly, so please enter it soon.", "REQUEST_NEW_CODE_TITLE": "Request a new code in {{ countdown }} seconds."}, "LABELS": {"EMAIL": "Email address:", "PASSWORD": "Password:", "CODE": "Code:"}, "PLACEHOLDERS": {"EMAIL": "Enter your email address...", "PASSWORD": "Enter password...", "CODE": "Enter magic code..."}, "VALIDATION": {"EMAIL_REQUIRED": "Email address is required!", "CODE_REQUIRED": "Code is required to log in.", "EMAIL_REAL_REQUIRED": "Email address should be the real one!", "PASSWORD_REQUIRED": "Password is required!", "PASSWORD_SHOULD_CONTAIN": "Password should contain from {{ minLength }} to {{ maxLength }} characters!", "PASSWORD_NO_SPACE_EDGES": "Passwords must not begin or end with spaces.", "CODE_REQUIRED_LENGTH": "Code should contain {{ requiredLength }} characters."}, "DEMO": {"TITLE": "Login Automatically into Demo accounts", "SUB_TITLE": "Please select account type below.", "SUPER_ADMIN_TITLE": "Super Admin", "ADMIN_TITLE": "Admin", "EMPLOYEE_TITLE": "Employee", "DEMO_TITLE": "Demo", "CREDENTIALS_TITLE": "Credentials", "LABELS": {"EMAIL": "Email: ", "PASSWORD": "Password: "}}}, "FORGOT_PASSWORD_PAGE": {"TITLE": "Forgot Password", "SUB_TITLE": "Enter your email address and we’ll send a link to reset your password", "ALERT_TITLE": "Oh snap!", "ALERT_SUCCESS_TITLE": "Hooray!", "REQUEST_PASSWORD_TEXT": "Request password", "BACK_TO_LOGIN": "Back to?", "FAQ_TITLE": "FAQ", "FAQ_LEARN_MORE": "Learn more", "LABELS": {"EMAIL": "Enter your email address:"}, "PLACEHOLDERS": {"EMAIL": "Email address"}, "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "EMAIL_REAL_REQUIRED": "Email should be the real one!"}}, "RESET_PASSWORD_PAGE": {"TITLE": "Change Password", "SUB_TITLE": "Please set a new password", "LABELS": {"NEW_PASSWORD": "New Password:", "CONFIRM_PASSWORD": "Confirm Password:", "BACK_TO": "Back to"}, "PLACEHOLDERS": {"NEW_PASSWORD": "New Password", "CONFIRM_PASSWORD": "Confirm Password"}, "VALIDATION": {"NEW_PASSWORD_REQUIRED": "New password is required!", "CONFIRM_PASSWORD_REQUIRED": "Password confirmation is required!", "PASSWORDS_NOT_MATCH": "Password does not match with the confirm password!", "PASSWORD_SHOULD_CONTAIN": "New password should contain from {{ minLength }} to {{ maxLength }} characters!"}}, "PAGINATION": {"ITEMS": "Items"}, "USER_MENU": {"STATUS": "Status", "AVAILABLE": "Available", "UNAVAILABLE": "Unavailable", "PAUSE_NOTIFICATIONS": "Pause notifications", "FOR_1_HOUR": "For 1hour", "FOR_2_HOURS": "For 2hours", "UNTIL_TOMORROW": "Until tomorrow", "CUSTOM": "Custom", "SET_AS_NOTIFICATION_SCHEDULE": "Set as notification schedule", "SET_YOURSELF_AS_AWAY": "Set yourself as <strong>away</strong> 😴", "SET_YOURSELF_AS_ACTIVE": "Set yourself as <strong>active</strong>", "HOTKEYS": "Hotkeys", "HELP": "Help", "SIGN_OUT": "Sign Out", "PROFILE": "Profile"}, "WORKSPACES": {"ADD_WORKSPACE": "Add a workspace", "LOADING": "Loading workspaces...", "MENUS": {"SING_ANOTHER_WORKSPACE": "Sign in another workspace", "CREATE_NEW_WORKSPACE": "Create a new workspace", "FIND_WORKSPACE": "Find workspace"}, "LABELS": {"EMAIL": "Enter your email address:", "PASSWORD": "Enter your password:"}, "PLACEHOLDERS": {"EMAIL": "<EMAIL>", "PASSWORD": "Password"}, "SIGN_IN_TITLE": "Sign in to Workspace", "BACK_TO": "Back to ", "SUCCESS_SIGNIN_TITLE": "Congrats! you’ve successfully signed in to your workspace.", "SUCCESS_SIGNIN_SUB_TITLE": "Please be patient while we are preparing the workspace...", "FAIL_SIGNIN_TITLE": "Error! the magic code or link you provided is expired or invalid.", "FAIL_SIGNIN_SUB_TITLE": "Please initiate the process again.", "THANKING_TEXT": "Thank you for choosing us.", "UNKNOWN_WORKSPACE": "Don't know your workspace?", "FIND_WORKSPACE": "Find your workspaces", "SELECTION": {"WELCOME_BACK": "Welcome Back!", "YOU_LOOK_NICE_TODAY": "You look nice today!", "EMAIL_MULTIPLE_WORKSPACE": "The email associated with multiple workspaces, please select one to continue", "SELECT_WORKSPACE_FOR": "Select Workspace for", "OPEN": "Open"}}, "NO_IMAGE": {"ADD_DROP": "Add or Drop Image", "AVAILABLE": "Image not available"}, "SERVER_API": "API Server", "AGENT_APP": "Agent", "AGENT_LOG": "Agent <PERSON>", "SERVER_API_LOG": "API Server Log", "COMING_SOON": "This page is coming soon!", "PLUGIN": {"VIDEO": {"SINGLE": "Video", "PLURAL": "Videos", "DOWNLOAD": "Download", "NOT_FOUND": "Video not found", "NO_VIDEO": "No video", "DOWNLOADS": "Downloads", "NO_VIDEO_DOWNLOAD": "No video download", "LOADING_VIDEOS": "Loading videos...", "EDIT_METADATA": "Edit video metadata", "STARTING_DOWNLOAD_FOR": "Starting download for: {{ url }}", "DOWNLOAD_FAILED_FOR": "Download failed for: {{ url }}, retry", "DOWNLOAD_COMPLETED_FOR": "Download completed for: {{ url }}", "SHARED_SUCCESSFULLY": "Video shared successfully", "COPIED_SUCCESSFULLY": "Video has been copied successfully", "CORE_INFO": "Core Information", "RECORDED": "Recorded", "DURATION": "Duration", "SIZE": "Size", "VIEW_FULL": "View Full Video", "TECHNICAL_DETAILS": "Technical Details", "RESOLUTION": "Resolution", "CODEC": "Codec", "FRAME_RATE": "Frame Rate", "UPLOADED_BY": "Uploaded By", "NO_VIDEO_AVAILABLE": "No video available", "RETRIED": "Video retried", "REMOVED_FROM_QUEUE": "Video removed from queue", "ADDED_TO_QUEUE": "Video added to queue", "DELETE_SUCCESSFULLY": "Video deleted successfully", "UPDATE_SUCCESSFULLY": "Video updated successfully", "ERROR": {"REQUIRED": "Title is required.", "AT_LEAST_3": "Title must be at least 3 characters long.", "AT_MOST_255": "Title cannot exceed 255 characters.", "AT_MOST_1000": "Description cannot exceed 1000 characters.", "SHARE_CANCELED": "Share was canceled", "INVALID_SHARE_DATA": "Invalid share data", "SHARE_NOT_ALLOWED": "Share is not allowed", "UNEXPECTED_SHARING_ERROR": "Unexpected sharing error"}}, "FORM": {"TITLE": "Upload Plugin", "BASIC_INFO": "Basic Information", "NAME": "Plugin Name", "NAME_PLACEHOLDER": "Enter plugin name", "VALIDATION": {"NAME_REQUIRED": "Plugin name is required", "NAME_TOO_LONG": "Plugin name is too long", "VERSION_REQUIRED": "Version is required", "VERSION_FORMAT": "Invalid version format", "DESCRIPTION_TOO_LONG": "Description is too long", "URL_REQUIRED": "URL is required", "INVALID_URL": "Invalid URL format", "PACKAGE_NAME_REQUIRED": "Package name is required", "INVALID_PACKAGE_NAME": "Invalid package name format", "INVALID_SCOPE": "Invalid scope format", "AUTHOR_TOO_LONG": "Author name is too long", "LICENSE_TOO_LONG": "License field is too long", "CHANGELOG_TOO_SHORT": "Changelog is too short", "CHANGELOG_REQUIRED": "Changelog is required", "FAILED": "Validation failed", "REQUIRED": "Required"}, "TYPE": "Plugin Type", "TYPE_PLACEHOLDER": "Select plugin type", "TYPES": {"DESKTOP": "Desktop", "WEB": "Web", "MOBILE": "Mobile"}, "VERSION": "Version", "STATUS": "Status", "STATUSES": {"ACTIVE": "Active", "INACTIVE": "Inactive", "DEPRECATED": "Deprecated", "ARCHIVED": "Archived"}, "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Enter plugin description", "SOURCE": "Source", "SOURCE_TYPE": "Source Type", "SOURCE_TYPES": {"CDN": "CDN", "NPM": "NPM", "GAUZY": "Gauzy"}, "CDN": {"URL": "CDN URL", "INTEGRITY": "Integrity", "CROSS_ORIGIN": "Cross Origin"}, "NPM": {"PACKAGE_NAME": "NPM Package Name", "REGISTRY": "NPM Registry", "SCOPE": "<PERSON><PERSON>", "AUTH_TOKEN": "Authentication Token"}, "FILE_UPLOAD": {"DRAG_DROP": "Drag & Drop your file here", "BROWSE": "Browse", "REMOVE": "Remove File", "RESTRICTIONS": "Only .zip files are allowed", "FILE": "File"}, "METADATA": {"TITLE": "<PERSON><PERSON><PERSON>", "AUTHOR": "Author", "AUTHOR_PLACEHOLDER": "Enter author name", "LICENSE": "License", "LICENSE_PLACEHOLDER": "Enter license", "HOMEPAGE": "Homepage", "HOMEPAGE_PLACEHOLDER": "Enter homepage URL", "REPOSITORY": "Repository", "REPOSITORY_PLACEHOLDER": "Enter repository URL"}, "OR": "Or", "RELEASE_DATE": "Release Date", "CHANGELOG": "Changelog", "CHANGELOG_PLACEHOLDER": "Enter changelog", "CLICK_TO_SELECT_SOURCE": "Click to select source", "SELECT_SOURCE_TYPE": "Select source type", "ADD_SOURCE": "Add new source", "OPERATING_SYSTEM": "Operating System", "ARCHITECTURE": "Architecture"}, "DETAILS": {"USAGE_STATS": "Usage Statistics", "DOWNLOAD_COUNT": "Download Count", "LAST_DOWNLOADED": "Last Downloaded", "UPLOADED_BY": "Uploaded By", "UPLOADED_AT": "Uploaded At", "SECURITY_INFO": "Security Information", "CHECKSUM": "Checksum", "SIGNATURE": "Signature", "INSTALLED": "Installed", "HIDDEN_FOR_SECURITY": "Hidden for security reasons"}, "MARKETPLACE": {"UPLOAD": "Upload New Plugin", "OFFLINE": "You are currently offline/disconnected ⛓️‍"}, "LAYOUT": {"DISCOVER": "Discover", "INSTALLED": "Installed", "VERSION_HISTORY": "Version History"}, "DIALOG": {"DEACTIVATE": {"TITLE": "Deactivate Plugin", "DESCRIPTION": "Are you sure you want to deactivate this plugin?", "CONFIRM": "Deactivate"}, "UNINSTALL": {"TITLE": "Uninstall Plugin", "DESCRIPTION": "Are you sure you want to uninstall this plugin?", "CONFIRM": "Uninstall"}, "DELETE": {"TITLE": "Delete Plugin", "DESCRIPTION": "Are you sure you want to delete this plugin?", "CONFIRM": "Delete"}, "VERSION": {"UPDATE": {"TITLE": "Update version v{{number}}", "DESCRIPTION": "Are you sure you want to update this plugin version?", "CONFIRM": "Update"}, "CREATE": {"TITLE": "Create new version for {{name}}", "DESCRIPTION": "Are you sure you want to create this version?", "CONFIRM": "Create"}, "DELETE": {"TITLE": "Delete version", "DESCRIPTION": "Are you sure you want to delete this version?", "CONFIRM": "Delete"}, "RESTORE": {"TITLE": "Restore version", "DESCRIPTION": "Are you sure you want to restore this version?", "CONFIRM": "Rest<PERSON>"}}, "SOURCE": {"CREATE": {"TITLE": "Create new source for version v{{ number }} of plugin {{ name }}", "DESCRIPTION": "Are you sure you want to create this version?", "CONFIRM": "Create"}}, "INSTALLATION": {"TITLE": "Install Plugin", "DESCRIPTION": "Are you sure you want to install this plugin?", "CONFIRM": "Install", "VALIDATION": {"REQUIRED": "Authentication token is required"}}}, "TOASTR": {"SUCCESS": {"VERSION": {"CREATED": "Create plugin version v{{number}} successfully!", "UPDATED": "Update plugin version v{{number}} successfully!", "DELETED": "Delete plugin version successfully!", "RESTORED": "Restore plugin version successfully!"}, "SOURCE": {"CREATED": "Create plugin source successfully!", "UPDATED": "Update plugin source successfully!", "DELETED": "Delete plugin source successfully!", "RESTORED": "Restore plugin source successfully!"}, "UPLOADED": "Upload plugin successfully!", "UPDATED": "Update plugin successfully!", "DELETED": "Delete plugin successfully!", "INSTALLED": "Install plugin successfully!", "UNINSTALLED": "Uninstall plugin successfully!"}, "INFO": {"VERSION": {"ADDING": "Adding new plugin version...", "UPDATING": "Updating plugin version...", "DELETING": "Deleting plugin version...", "RESTORING": "Restoring plugin version..."}, "SOURCE": {"ADDING": "Adding new source...", "UPDATING": "Updating plugin version...", "DELETING": "Deleting plugin version...", "RESTORING": "Restoring plugin version..."}, "UPLOADING": "Uploading plugin...", "UPDATING": "Updating plugin version...", "DELETING": "Deleting plugin...", "INSTALLING": "Installing plugin...", "UNINSTALLING": "Uninstalling plugin..."}, "ERROR": {"VERSION": {"RESTORE": "Failed to restore plugin version. Please try again!"}, "SOURCE": {"RESTORE": "Failed to restore plugin source. Please try again!"}, "UPLOAD": "Failed to upload plugin. Please try again!", "NOT_FOUND": "Plugin with ID {{ id }} not found.", "INSTALL": "Failed to install plugin. Please try again!", "REVERT_INSTALLATION": "Failed to revert installation!"}}}, "SERVER_DOWN": {"TITLE": "Server Unavailable", "DESCRIPTION": "The {{companySite}} server is currently unreachable. This may be due to scheduled maintenance or unexpected downtime.", "CONNECTING": "Automatically retrying connection...", "HINT": "Status: Attempting to establish connection to {{companySite}} servers. Error code: 503."}}