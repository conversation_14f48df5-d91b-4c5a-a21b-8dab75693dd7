import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { Store } from '../services/store/store.service';
import { AppInitializationService } from '../services/app-initialization/app-initialization.service';

/**
 * Guard that ensures workspaces are loaded before allowing navigation.
 * This guard can be used on routes that require workspace data to be available.
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceInitializationGuard implements CanActivate {
	constructor(
		private readonly store: Store,
		private readonly appInitializationService: AppInitializationService
	) {}

	canActivate(
		route: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> | Promise<boolean> | boolean {
		// Check if user is authenticated
		const user = this.store.user;
		if (!user?.id) {
			// No authenticated user, allow navigation (auth guard should handle this)
			return true;
		}

		// Check if workspaces are already loaded
		if (this.appInitializationService.isUserDataInitialized()) {
			return true;
		}

		// Load workspaces and allow navigation when done
		return this.appInitializationService.initializeWorkspaces().pipe(
			map(() => true),
			catchError((error) => {
				console.error('Failed to initialize workspaces in guard:', error);
				// Allow navigation even if workspace loading fails
				// The components can handle the empty state
				return of(true);
			})
		);
	}
}
