import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap, catchError, switchMap } from 'rxjs/operators';
import { IUser } from '@gauzy/contracts';
import { Store } from '../store/store.service';
import { WorkspaceService } from '../workspace/workspace.service';

/**
 * Service responsible for initializing application data after user authentication.
 * This service ensures that essential data like workspaces are loaded into the store
 * when the user logs in or when the application starts with an authenticated user.
 */
@Injectable({
	providedIn: 'root'
})
export class AppInitializationService {
	constructor(
		private readonly store: Store,
		private readonly workspaceService: WorkspaceService
	) {}

	/**
	 * Initialize application data for an authenticated user.
	 * This method should be called after user login or when the app starts with an authenticated user.
	 *
	 * @param user - The authenticated user (optional, will use store.user if not provided)
	 * @returns Observable that completes when initialization is done
	 */
	initializeUserData(user?: IUser): Observable<any> {
		const currentUser = user || this.store.user;
		
		if (!currentUser?.id) {
			console.warn('Cannot initialize user data: no authenticated user found');
			return of(null);
		}

		console.log('Initializing application data for user:', currentUser.email);

		// Load workspaces into the store
		return this.workspaceService.getWorkspaces().pipe(
			tap((workspaces) => {
				console.log(`Loaded ${workspaces.length} workspaces into store`);
			}),
			catchError((error) => {
				console.error('Failed to initialize user workspaces:', error);
				// Don't fail the entire initialization if workspaces fail to load
				return of([]);
			})
		);
	}

	/**
	 * Initialize workspaces specifically.
	 * This is a convenience method that can be called independently.
	 *
	 * @returns Observable of workspaces
	 */
	initializeWorkspaces(): Observable<any> {
		return this.workspaceService.getWorkspaces().pipe(
			tap((workspaces) => {
				console.log(`Initialized ${workspaces.length} workspaces`);
			}),
			catchError((error) => {
				console.error('Failed to initialize workspaces:', error);
				return of([]);
			})
		);
	}

	/**
	 * Clear all application data.
	 * This method should be called when the user logs out.
	 */
	clearUserData(): void {
		console.log('Clearing application data');
		this.workspaceService.clearWorkspaces();
		// Add other data clearing logic here as needed
	}

	/**
	 * Re-initialize application data.
	 * This method can be called when switching users or after certain operations.
	 *
	 * @param user - The user to initialize data for
	 * @returns Observable that completes when re-initialization is done
	 */
	reinitializeUserData(user?: IUser): Observable<any> {
		// Clear existing data first
		this.clearUserData();
		
		// Then initialize with new data
		return this.initializeUserData(user);
	}

	/**
	 * Check if user data is initialized.
	 * This can be used to determine if initialization is needed.
	 *
	 * @returns boolean indicating if user data appears to be initialized
	 */
	isUserDataInitialized(): boolean {
		const user = this.store.user;
		const workspaces = this.store.workspaces;
		
		// Consider data initialized if we have a user and workspaces
		return !!(user?.id && workspaces && workspaces.length > 0);
	}
}
