import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { IWorkSpace, IUserSigninWorkspaceResponse, IUser } from '@gauzy/contracts';
import { AuthService } from '../auth/auth.service';
import { Store } from '../store/store.service';


/**
 * Service for managing workspaces with store integration.
 * Provides centralized workspace management with caching and state management.
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceService {
	constructor(
		private readonly authService: AuthService,
		private readonly store: Store,
		
	) {}

	/**
	 * Get workspaces from store or fetch from API if not cached.
	 * This method ensures workspaces are loaded and cached in the store.
	 *
	 * @param forceRefresh - Force refresh from API even if cached
	 * @returns Observable of workspaces array
	 */
	getWorkspaces(forceRefresh = false): Observable<IWorkSpace[]> {
		// Check if workspaces are already loaded and not forcing refresh
		const currentWorkspaces = this.store.workspaces;
		if (!forceRefresh && currentWorkspaces && currentWorkspaces.length > 0) {
			return of(currentWorkspaces);
		}

		// Set loading state
		this.store.setWorkspacesLoading(true);

		return this.authService.getUserWorkspaces(false).pipe(
			map(({ workspaces }: IUserSigninWorkspaceResponse) => {
				const user = this.store.user;
				const mappedWorkspaces: IWorkSpace[] = workspaces.map((workspace) => ({
					id: workspace.user.tenant.id,
					name: workspace.user.tenant.name,
					imgUrl: workspace.user.tenant.logo || '/assets/images/default.svg',
					isOnline: true,
					isSelected: workspace.user.tenant.id === user?.tenantId
				}));

				// Update store with workspaces
				this.store.setWorkspaces(mappedWorkspaces, user?.tenantId);

				return mappedWorkspaces;
			}),
			catchError((error) => {
				console.error('Error fetching workspaces:', error);
				this.store.setWorkspacesLoading(false, 'Failed to load workspaces');
				
				return of([]);
			})
		);
	}

	/**
	 * Get the currently selected workspace from the store.
	 *
	 * @returns The selected workspace or null
	 */
	getSelectedWorkspace(): IWorkSpace | null {
		return this.store.selectedWorkspace;
	}

	/**
	 * Set the selected workspace in the store.
	 *
	 * @param workspace - The workspace to select
	 */
	setSelectedWorkspace(workspace: IWorkSpace): void {
		// Update workspace states
		const currentWorkspaces = this.store.workspaces;
		const updatedWorkspaces = currentWorkspaces.map((w: IWorkSpace) => ({
			...w,
			isSelected: w.id === workspace.id
		}));

		// Update store
		this.store.setWorkspaces(updatedWorkspaces, workspace.id);
	}

	/**
	 * Refresh workspaces from the API and update the store.
	 *
	 * @returns Observable of workspaces array
	 */
	refreshWorkspaces(): Observable<IWorkSpace[]> {
		return this.getWorkspaces(true);
	}

	/**
	 * Check if workspaces are currently loading.
	 *
	 * @returns Observable of loading state
	 */
	isLoading(): Observable<boolean> {
		return this.store.workspacesLoading$;
	}

	/**
	 * Get the current error state for workspaces.
	 *
	 * @returns Observable of error message or null
	 */
	getError(): Observable<string | null> {
		return this.store.workspacesError$;
	}

	/**
	 * Clear workspaces from the store.
	 * Useful when logging out or switching users.
	 */
	clearWorkspaces(): void {
		this.store.setWorkspaces([], null);
	}

	/**
	 * Get workspaces observable from the store.
	 * Use this for reactive components that need to observe workspace changes.
	 *
	 * @returns Observable of workspaces array
	 */
	getWorkspaces$(): Observable<IWorkSpace[]> {
		return this.store.workspaces$;
	}

	/**
	 * Get selected workspace observable from the store.
	 * Use this for reactive components that need to observe selected workspace changes.
	 *
	 * @returns Observable of selected workspace
	 */
	getSelectedWorkspace$(): Observable<IWorkSpace> {
		return this.store.selectedWorkspace$;
	}

	/**
	 * Initialize workspaces for a user.
	 * This method should be called after user login to ensure workspaces are loaded.
	 *
	 * @param user - The authenticated user
	 * @returns Observable of workspaces array
	 */
	initializeWorkspaces(user: IUser): Observable<IWorkSpace[]> {
		if (!user?.id) {
			return of([]);
		}

		return this.getWorkspaces();
	}
}
