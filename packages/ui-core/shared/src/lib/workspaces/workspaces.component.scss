@use 'themes' as *;

div {
  margin-bottom: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  z-index: 1050;
  cursor: pointer;

  &.workspace-item {
    border-radius: nb-theme(border-radius);
    transition: background-color 0.2s ease;
  }
  button {
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 0.5rem;
  }
  .img-container {
    object-fit: cover;
    position: relative;

    img {
      width: 2.25rem;
      height: 2.25rem;
      border-radius: 0.5rem;
      margin: 0;
    }
    > div {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: #00d060;
      border-radius: 8px;
      border: 2px solid #ebebeb;
      right: 0;
      top: 0;
    }
  }

  .workspace-name {
    font-size: 0.875rem;
    font-weight: 500;
    padding-left: 0.5rem;
    color: nb-theme(text-basic-color);
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
  &.selected .img-container {
    border: 2px solid var(--color-primary-default);
    mix-blend-mode: multiply;
    border-radius: 0.5rem;
  }
}

::ng-deep {
  nb-context-menu {
    nb-menu .menu-item {
      border-width: 0;
      a {
        text-align: left;
        border-radius: nb-theme(border-radius);
        &:hover {
          background-color: nb-theme(color-primary-transparent-default);
        }
      }
    }
  }
  nb-context-menu,
  .context-menu {
    border-radius: 0 nb-theme(border-radius) nb-theme(border-radius) nb-theme(border-radius);
    padding: 0.3125rem;
  }
}
