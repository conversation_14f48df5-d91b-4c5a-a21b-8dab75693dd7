@use 'themes' as *;

div {
  margin-bottom: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  z-index: 1050;
  cursor: pointer;

  &.workspace-item {
    border-radius: nb-theme(border-radius);
    transition: background-color 0.2s ease;
  }

  .img-container {
    object-fit: cover;
    position: relative;

    img {
      width: 2.25rem;
      height: 2.25rem;
      border-radius: 0.5rem;
      margin: 0;
    }
    > div {
      position: absolute;
      width: 10px;
      height: 10px;
      background-color: #00d060;
      border-radius: 8px;
      border: 2px solid #ebebeb;
      right: 0;
      top: 0;
    }
  }

  .workspace-name {
    font-size: 0.875rem;
    font-weight: 500;
    padding-left: 0.5rem;
    color: nb-theme(text-basic-color);
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
  &.selected .img-container {
    border: 2px solid var(--color-primary-default);
    mix-blend-mode: multiply;
    border-radius: 0.5rem;
  }
}

// Add workspace button styling
.add-workspace-container {
  margin-bottom: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .add-workspace-btn {
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 0.5rem;
    border: 2px dashed nb-theme(border-basic-color-4);
    background: transparent;
    color: nb-theme(text-hint-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      border-color: nb-theme(color-primary-default);
      color: nb-theme(color-primary-default);
      background: nb-theme(color-primary-transparent-hover);
    }

    &:active,
    &:focus {
      border-color: nb-theme(color-primary-default);
      color: nb-theme(color-primary-default);
      background: transparent;
      box-shadow: none;
      outline: none;
    }

    nb-icon {
      font-size: 1rem;
    }
  }
}

::ng-deep {
  nb-context-menu {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid nb-theme(border-basic-color-3);
    padding: 8px;
    min-width: 280px;
    max-width: 320px;
    background: nb-theme(background-basic-color-1);

    nb-menu {
      .menu-item {
        border-width: 0;
        margin: 0 0 2px 0;
        border-radius: 6px;

        a {
          text-align: left;
          border-radius: 6px;
          padding: 10px 12px;
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 14px;
          color: nb-theme(text-basic-color);
          transition: all 0.15s ease;
          text-decoration: none;

          &:hover {
            background-color: #007acc;
            color: #ffffff;

            nb-icon {
              background-color: rgba(255, 255, 255, 0.2);
              color: #ffffff;
            }
          }

          &:active {
            background-color: #005a9e;
            color: #ffffff;
          }

          // Icon container with larger background like Slack
          nb-icon {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #495057;
            flex-shrink: 0;
            transition: all 0.15s ease;
          }

          // Dark mode support
          [data-nb-theme='dark'] & {
            nb-icon {
              background-color: #3a3a3a;
              color: #e0e0e0;
            }

            &:hover {
              nb-icon {
                background-color: rgba(255, 255, 255, 0.15);
                color: #ffffff;
              }
            }
          }

          .menu-title {
            font-weight: 400;
            line-height: 1.4;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
