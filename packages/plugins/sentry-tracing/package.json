{"name": "@gauzy/plugin-sentry", "version": "0.1.0", "description": "Gauzy Sentry Plugin - Seamless integration with Sentry for advanced error tracking and monitoring in Gauzy Platform.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/sentry-tracing"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-sentry", "lib:build:prod": "yarn nx build plugin-sentry", "lib:watch": "yarn nx build plugin-sentry --watch"}, "dependencies": {"@gauzy/config": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/graphql": "^13.1.0", "@sentry/node": "^9.43.0", "chalk": "^4.1.0", "express": "^5.1.0", "rxjs": "^7.8.2", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["gauzy", "sentry", "plugin", "error-tracking", "monitoring", "<PERSON><PERSON><PERSON>", "typescript", "logging", "observability"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}