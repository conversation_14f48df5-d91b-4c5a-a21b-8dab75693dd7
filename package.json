{"name": "ever-gauzy", "version": "0.1.0", "description": "Ever Gauzy - Business Management Platform (ERP/CRM/HRM/ATS/PM)", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "keywords": [], "scripts": {"prepare:husky": "npx husky install .husky", "ng": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx", "ng:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx", "ng:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx", "ng:ci": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx", "config": "yarn ts-node ./.scripts/configure.ts", "config:dev": "yarn run config -- --environment=dev", "config:prod": "yarn run config -- --environment=prod", "config:electron": "yarn ts-node ./.scripts/electron.env.ts", "config:desktop:prod": "yarn run config:electron -- --environment=prod --desktop=desktop", "config:desktop-timer:prod": "yarn run config:electron -- --environment=prod --desktop=desktop-timer", "config:server:prod": "yarn run config:electron -- --environment=prod --desktop=server", "config:server-api:prod": "yarn run config:electron -- --environment=prod --desktop=server-api", "start": "yarn build:package:all && yarn concurrently --raw --kill-others \"yarn start:api\" \"yarn start:gauzy\"", "start:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn build:package:all && yarn concurrently --raw --kill-others \"yarn start:api:prod\" \"yarn start:gauzy:prod\"", "start:gauzy": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn run postinstall.web && yarn build:package:ui-config && yarn ng serve gauzy --open", "start:gauzy:forever": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn run config:dev && forever start node_modules/@angular/cli/bin/ng serve gauzy --disable-host-check --host 0.0.0.0", "start:gauzy:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run config:prod && yarn ng serve gauzy --configuration production --disable-host-check --host 0.0.0.0 --prod", "start:api": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn ng serve api", "start:api:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn ng:prod serve api --host 0.0.0.0 -c=production --prod", "start:api:ci": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn --frozen-lockfile --cache-folder ~/.cache/yarn ng:ci serve api -c=production --prod", "start:api:forever": "cross-env NODE_OPTIONS=--max-old-space-size=12288 forever start node_modules/@angular/cli/bin/ng serve api --host 0.0.0.0", "start:api:core": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx run core:serve", "db:migration": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn run build:package:api && yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json ./apps/api/src/migration.ts", "migration:run": "yarn db:migration migration:run", "migration:revert": "yarn db:migration migration:revert", "migration:generate": "yarn db:migration migration:generate", "migration:create": "yarn db:migration migration:create", "migration:command": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn --cwd ./packages/core", "seed:base": "cross-env NODE_OPTIONS=--max-old-space-size=12288 yarn run config:dev && yarn run build:package:api && yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json", "seed": "yarn seed:base ./apps/api/src/seed.ts", "seed:ever": "yarn seed:base ./apps/api/src/seed-ever.ts", "seed:all": "yarn seed:base ./apps/api/src/seed-all.ts", "seed:jobs": "yarn seed:base ./apps/api/src/seed-jobs.ts", "seed:module": "yarn seed:base ./apps/api/src/seed-module.ts --name", "seed:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:package:api:prod && yarn run config:prod && yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json ./apps/api/src/seed.ts", "prebuild": "rimraf dist coverage", "bootstrap": "yarn install --ignore-scripts && yarn postinstall.manual", "build": "yarn build:package:all && concurrently --raw \"yarn build:api\" \"yarn build:gauzy\"", "build:lerna": "yarn lerna run --parallel build", "build:prod": "yarn build:package:all && concurrently --raw \"yarn build:api:prod\" \"yarn build:gauzy:prod\"", "build:gauzy": "yarn run postinstall.web && yarn run config:dev && yarn ng build gauzy", "build:gauzy:prod": "yarn run config:prod && yarn ng:prod build gauzy -c=production --prod --verbose", "build:gauzy:prod:docker": "yarn run config:prod && yarn ng:docker build gauzy -c=production --prod --verbose", "build:gauzy:prod:ci": "yarn run config:prod && yarn --frozen-lockfile --cache-folder ~/.cache/yarn ng:ci build gauzy -c=production --prod --verbose", "build:api": "yarn ng build api --configuration=development", "build:api:prod": "yarn ng:prod build api --configuration=production", "build:api:prod:docker": "yarn ng:docker build api --configuration=production", "build:api:prod:ci": "yarn --frozen-lockfile --cache-folder ~/.cache/yarn ng:ci build api -c=production --prod", "build:watch": "ts-node ./.scripts/build-watch.ts", "bundle-report": "yarn run config:prod && yarn ng:prod build -c=production --stats-json && webpack-bundle-analyzer dist/apps/gauzy/stats.json", "commit": "git-cz", "commit:lint": "commitlint -E HUSKY_GIT_PARAMS", "semantic-release": "semantic-release", "test": "yarn run postinstall.web && yarn run config:dev && yarn ng test", "lint": "yarn run config:dev && yarn ng lint && yarn ng lint", "e2e": "yarn run postinstall.web && yarn run config:dev && yarn ng e2e --browser chrome", "e2e:ci": "yarn run postinstall.web && yarn run config:prod && yarn --frozen-lockfile --cache-folder ~/.cache/yarn ng:ci e2e -c=production --prod --headless", "affected:apps": "yarn nx affected:apps", "affected:libs": "yarn nx affected:libs", "affected:build": "yarn nx affected:build", "affected:e2e": "yarn nx affected:e2e", "affected:test": "yarn nx affected:test", "affected:lint": "yarn nx affected:lint", "affected:dep-graph": "yarn nx affected:dep-graph", "affected": "yarn nx affected", "format": "yarn nx format:write", "format:write": "yarn nx format:write", "format:check": "yarn nx format:check", "update": "yarn ng update @nx/workspace", "update:check": "yarn ng update", "workspace-schematic": "yarn nx workspace-schematic", "dep-graph": "yarn nx dep-graph", "help": "yarn nx help", "doc:build": "compodoc -p tsconfig.json -d dist/docs", "doc:serve": "compodoc -s -d dist/docs", "doc:build-serve": "compodoc -p tsconfig.json -d docs -s", "postinstall.manual": "yarn ts-node ./.scripts/postinstall.js", "postinstall.electron": "yarn electron-builder install-app-deps && yarn node tools/electron/postinstall", "postinstall.web": "yarn node ./decorate-angular-cli.js && yarn node tools/web/postinstall", "build:desktop": "cross-env NODE_ENV=production yarn run copy-files-i18n-desktop && yarn run postinstall.electron && yarn run config:prod && yarn run config:desktop:prod && yarn run build:package:all:prod && yarn run pack:desktop && yarn run generate:icons:desktop --environment=prod && yarn ng:prod run gauzy:desktop-ui --base-href ./ && yarn run prepare:desktop && yarn ng:prod run api:desktop-api && yarn ng:prod build desktop-api --output-path=dist/apps/desktop/desktop-api && yarn ng:prod build desktop --base-href ./ && yarn run copy-files-desktop && yarn run copy-assets-gauzy", "build:desktop:local": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop && electron dist/apps/desktop", "build:desktop:linux": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --project dist/apps/desktop", "build:desktop:linux:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/desktop", "build:desktop:linux:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/desktop", "build:desktop:windows": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --project dist/apps/desktop", "build:desktop:windows:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/desktop", "build:desktop:windows:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/desktop", "build:desktop:mac": "yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --project dist/apps/desktop", "build:desktop:mac:release": "yarn run build:desktop && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish=always --project dist/apps/desktop", "build:desktop:mac:quick": "npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --project dist/apps/desktop", "prepare:desktop": "yarn run postinstall.electron && tsc -p apps/desktop/tsconfig.electron.json", "serve:desktop.target": "yarn ng serve desktop", "serve:desktop": "wait-on http-get://localhost:4200/ && electron apps/desktop/src --serve", "start:desktop": "yarn run prepare:desktop && npm-run-all -p serve.electron.gauzy-desktop.target serve.electron.gauzy-desktop", "build:desktop:local:quick": "yarn electron dist/apps/desktop --prod", "build:desktop:windows:quick": "npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --windows --project dist/apps/desktop", "debug:desktop:app:mac": "lldb ./dist/apps/desktop-packages/mac/GauzyDesktop.app && run", "start:desktop:ui": "yarn run postinstall.web && yarn ng serve desktop", "build:desktop:ui": "yarn run postinstall.web && yarn ng:prod build desktop --base-href ./", "prepare:desktop:dev": "yarn run postinstall.electron && tsc -p apps/desktop/tsconfig.dev.json", "start:api:desktop": "yarn ng serve api-desktop", "deploy:desktop:mac": "npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish always --project dist/apps/desktop", "build:package:ui-auth": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build ui-auth --configuration=development", "build:package:ui-auth:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build ui-auth --configuration=production", "build:package:ui-auth:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build ui-auth --configuration=production", "build:package:ui-config": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 && yarn nx build ui-config --configuration=development", "build:package:ui-config:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build ui-config --configuration=production", "build:package:ui-config:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build ui-config --configuration=production", "build:package:ui-core": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build ui-core --configuration=development", "build:package:ui-core:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build ui-core --configuration=production", "build:package:ui-core:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build ui-core --configuration=production", "build:package:auth": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build auth --configuration=development", "build:package:auth:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build auth --configuration=production", "build:package:auth:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build auth --configuration=production", "build:package:core": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build core", "build:package:core:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build core", "build:package:core:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build core", "build:package:common": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build common", "build:package:common:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build common", "build:package:common:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build common", "build:package:utils": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build utils", "build:package:utils:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build utils", "build:package:utils:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build utils", "build:package:constants": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build constants", "build:package:constants:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build constants", "build:package:constants:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build constants", "build:package:contracts": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build contracts", "build:package:contracts:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build contracts", "build:package:contracts:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build contracts", "build:package:config": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build config", "build:package:config:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build config", "build:package:config:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build config", "build:package:desktop-core": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-core", "build:package:desktop-core:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-core", "build:package:desktop-core:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build desktop-core", "build:package:desktop-window": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn run build:package:desktop-core && yarn nx build desktop-window", "build:package:desktop-window:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:package:desktop-core:prod && yarn nx build desktop-window", "build:package:desktop-window:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:package:desktop-core:docker && yarn nx build desktop-window", "build:package:desktop-lib": "yarn run build:package:desktop-window && cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-lib", "build:package:desktop-lib:prod": "yarn run build:package:desktop-window:prod && cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-lib", "build:package:desktop-lib:docker": "yarn run build:package:desktop-window:prod && cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build desktop-lib", "build:package:desktop-ui-lib": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-ui-lib --configuration=development", "build:package:desktop-ui-lib:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-ui-lib --configuration=production", "build:package:desktop-ui-lib:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build desktop-ui-lib --configuration=production", "build:package:plugin": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin", "build:package:plugin:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin", "build:package:plugin:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin", "build:package:plugins:pre": "yarn run build:package:ui-core && yarn run build:package:ui-auth && yarn run build:package:plugin:onboarding-ui && yarn run build:package:plugin:legal-ui && yarn run build:package:plugin:job-search-ui && yarn run build:package:plugin:job-matching-ui && yarn run build:package:plugin:job-employee-ui && yarn run build:package:plugin:job-proposal-ui && yarn run build:package:plugin:public-layout-ui && yarn run build:package:plugin:maintenance-ui && yarn run build:package:plugin:videos-ui && yarn run build:integration-ui-plugins && yarn run build:package:plugin:posthog-ui", "build:package:plugins:pre:prod": "yarn run build:package:ui-core:prod && yarn run build:package:ui-auth:prod && yarn run build:package:plugin:onboarding-ui:prod && yarn run build:package:plugin:legal-ui:prod && yarn run build:package:plugin:job-search-ui:prod && yarn run build:package:plugin:job-matching-ui:prod && yarn run build:package:plugin:job-employee-ui:prod && yarn run build:package:plugin:job-proposal-ui:prod && yarn run build:package:plugin:public-layout-ui:prod && yarn run build:package:plugin:maintenance-ui:prod && yarn run build:package:plugin:videos-ui:prod && yarn run build:integration-ui-plugins:prod && yarn run build:package:plugin:posthog-ui:prod", "build:package:plugins:pre:docker": "yarn run build:package:ui-core:docker && yarn run build:package:ui-auth:docker && yarn run build:package:plugin:onboarding-ui:docker && yarn run build:package:plugin:legal-ui:docker && yarn run build:package:plugin:job-search-ui:docker && yarn run build:package:plugin:job-matching-ui:docker && yarn run build:package:plugin:job-employee-ui:docker && yarn run build:package:plugin:job-proposal-ui:docker && yarn run build:package:plugin:public-layout-ui:docker && yarn run build:package:plugin:maintenance-ui:docker && yarn run build:package:plugin:videos-ui:docker && yarn run build:integration-ui-plugins:docker && yarn run build:package:plugin:posthog-ui:docker", "build:package:plugins:post": "yarn run build:package:plugin:integration-jira && yarn run build:package:plugin:integration-activepieces && yarn run build:package:plugin:integration-ai && yarn run build:package:plugin:sentry && yarn run build:package:plugin:posthog && yarn run build:package:plugin:jitsu-analytic && yarn run build:package:plugin:product-reviews && yarn run build:package:plugin:job-search && yarn run build:package:plugin:job-proposal && yarn run build:package:plugin:integration-github && yarn run build:package:plugin:knowledge-base && yarn run build:package:plugin:changelog && yarn run build:package:plugin:integration-hubstaff && yarn run build:package:plugin:integration-upwork && yarn run build:package:plugin:integration-make-com && yarn run build:package:plugin:videos && yarn run build:package:plugin:registry && yarn run build:package:plugin:integration-zapier && yarn run build:package:plugin:soundshot && yarn run build:package:plugin:camshot", "build:package:plugins:post:prod": "yarn run build:package:plugin:integration-jira:prod && yarn run build:package:plugin:integration-activepieces:prod && yarn run build:package:plugin:integration-ai:prod && yarn run build:package:plugin:sentry:prod && yarn run build:package:plugin:posthog:prod && yarn run build:package:plugin:jitsu-analytic:prod && yarn run build:package:plugin:product-reviews:prod && yarn run build:package:plugin:job-search:prod && yarn run build:package:plugin:job-proposal:prod && yarn run build:package:plugin:integration-github:prod && yarn run build:package:plugin:knowledge-base:prod && yarn run build:package:plugin:changelog:prod && yarn run build:package:plugin:integration-hubstaff:prod && yarn run build:package:plugin:integration-upwork:prod && yarn run build:package:plugin:integration-make-com:prod && yarn run build:package:plugin:videos:prod && yarn run build:package:plugin:registry:prod && yarn run build:package:plugin:integration-zapier:prod && yarn run build:package:plugin:soundshot:prod && yarn run build:package:plugin:camshot:prod", "build:package:plugins:post:docker": "yarn run build:package:plugin:integration-jira:docker && yarn run build:package:plugin:integration-activepieces:docker && yarn run build:package:plugin:integration-ai:docker && yarn run build:package:plugin:sentry:docker && yarn run build:package:plugin:posthog:docker && yarn run build:package:plugin:jitsu-analytic:docker && yarn run build:package:plugin:product-reviews:docker && yarn run build:package:plugin:job-search:docker && yarn run build:package:plugin:job-proposal:docker && yarn run build:package:plugin:integration-github:docker && yarn run build:package:plugin:knowledge-base:docker && yarn run build:package:plugin:changelog:docker && yarn run build:package:plugin:integration-hubstaff:docker && yarn run build:package:plugin:integration-upwork:docker && yarn run build:package:plugin:integration-make-com:docker && yarn run build:package:plugin:videos:docker && yarn run build:package:plugin:registry:docker && yarn run build:package:plugin:integration-zapier:docker && yarn run build:package:plugin:soundshot:docker && yarn run build:package:plugin:camshot:docker", "build:package:plugin:integration-ai": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-ai", "build:package:plugin:integration-ai:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-ai", "build:package:plugin:integration-ai:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-ai", "build:package:plugin:integration-activepieces": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-activepieces", "build:package:plugin:integration-activepieces:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-activepieces", "build:package:plugin:integration-activepieces:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-activepieces", "build:package:plugin:integration-hubstaff": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-hubstaff", "build:package:plugin:integration-hubstaff:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-hubstaff", "build:package:plugin:integration-hubstaff:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-hubstaff", "build:package:plugin:integration-upwork": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-upwork", "build:package:plugin:integration-upwork:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-upwork", "build:package:plugin:integration-upwork:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-upwork", "build:package:plugin:integration-github": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-github", "build:package:plugin:integration-github:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-github", "build:package:plugin:integration-github:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-github", "build:package:plugin:integration-jira": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-jira", "build:package:plugin:integration-jira:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-jira", "build:package:plugin:integration-jira:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-jira", "build:package:plugin:integration-make-com": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-make-com", "build:package:plugin:integration-make-com:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-make-com", "build:package:plugin:integration-make-com:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-make-com", "build:package:plugin:integration-zapier": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-zapier", "build:package:plugin:integration-zapier:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-zapier", "build:package:plugin:integration-zapier:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-zapier", "build:package:plugin:sentry": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-sentry", "build:package:plugin:sentry:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-sentry", "build:package:plugin:sentry:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-sentry", "build:package:plugin:posthog": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-posthog", "build:package:plugin:posthog:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-posthog", "build:package:plugin:posthog:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-posthog", "build:package:plugin:jitsu-analytic": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-jitsu-analytics", "build:package:plugin:jitsu-analytic:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-jitsu-analytics", "build:package:plugin:jitsu-analytic:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-jitsu-analytics", "build:package:plugin:product-reviews": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-product-reviews", "build:package:plugin:product-reviews:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-product-reviews", "build:package:plugin:product-reviews:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-product-reviews", "build:package:plugin:job-search": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-search", "build:package:plugin:job-search:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-search", "build:package:plugin:job-search:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-job-search", "build:package:plugin:posthog-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-posthog-ui", "build:package:plugin:posthog-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-posthog-ui", "build:package:plugin:posthog-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-posthog-ui", "build:package:plugin:integration-ai-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-ai-ui --configuration=development", "build:package:plugin:integration-ai-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-ai-ui --configuration=production", "build:package:plugin:integration-ai-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-ai-ui --configuration=production", "build:package:plugin:integration-hubstaff-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-hubstaff-ui --configuration=development", "build:package:plugin:integration-hubstaff-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-hubstaff-ui --configuration=production", "build:package:plugin:integration-hubstaff-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-hubstaff-ui --configuration=production", "build:package:plugin:integration-github-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-github-ui --configuration=development", "build:package:plugin:integration-github-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-github-ui --configuration=production", "build:package:plugin:integration-github-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-github-ui --configuration=production", "build:package:plugin:integration-upwork-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-upwork-ui --configuration=development", "build:package:plugin:integration-upwork-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-upwork-ui --configuration=production", "build:package:plugin:integration-upwork-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-upwork-ui --configuration=production", "build:package:plugin:integration-make-com-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-make-com-ui --configuration=development", "build:package:plugin:integration-make-com-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-make-com-ui --configuration=production", "build:package:plugin:integration-make-com-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-make-com-ui --configuration=production", "build:package:plugin:integration-zapier-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-zapier-ui --configuration=development", "build:package:plugin:integration-zapier-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-zapier-ui --configuration=production", "build:package:plugin:integration-zapier-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-zapier-ui --configuration=production", "build:integration-ui-plugins": "yarn run build:package:plugin:integration-ai-ui && yarn run build:package:plugin:integration-hubstaff-ui && yarn run build:package:plugin:integration-upwork-ui && yarn run build:package:plugin:integration-github-ui && yarn run build:package:plugin:integration-make-com-ui && yarn run build:package:plugin:integration-zapier-ui", "build:integration-ui-plugins:prod": "yarn run build:package:plugin:integration-ai-ui:prod && yarn run build:package:plugin:integration-hubstaff-ui:prod && yarn run build:package:plugin:integration-upwork-ui:prod && yarn run build:package:plugin:integration-github-ui:prod && yarn run build:package:plugin:integration-make-com-ui:prod && yarn run build:package:plugin:integration-zapier-ui:prod", "build:integration-ui-plugins:docker": "yarn run build:package:plugin:integration-ai-ui:docker && yarn run build:package:plugin:integration-hubstaff-ui:docker && yarn run build:package:plugin:integration-upwork-ui:docker && yarn run build:package:plugin:integration-github-ui:docker && yarn run build:package:plugin:integration-make-com-ui:docker && yarn run build:package:plugin:integration-zapier-ui:docker", "build:package:plugin:job-employee-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-employee-ui --configuration=development", "build:package:plugin:job-employee-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-employee-ui --configuration=production", "build:package:plugin:job-employee-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-job-employee-ui --configuration=production", "build:package:plugin:job-matching-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-matching-ui --configuration=development", "build:package:plugin:job-matching-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-matching-ui --configuration=production", "build:package:plugin:job-matching-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-job-matching-ui --configuration=production", "build:package:plugin:job-proposal-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-proposal-ui --configuration=development", "build:package:plugin:job-proposal-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-proposal-ui --configuration=production", "build:package:plugin:job-proposal-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-job-proposal-ui --configuration=production", "build:package:plugin:job-search-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-search-ui --configuration=development", "build:package:plugin:job-search-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-search-ui --configuration=production", "build:package:plugin:job-search-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-job-search-ui --configuration=production", "build:package:plugin:legal-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-legal-ui --configuration=development", "build:package:plugin:legal-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-legal-ui --configuration=production", "build:package:plugin:legal-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-legal-ui --configuration=production", "build:package:plugin:maintenance-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-maintenance-ui --configuration=development", "build:package:plugin:maintenance-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-maintenance-ui --configuration=production", "build:package:plugin:maintenance-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-maintenance-ui --configuration=production", "build:package:plugin:onboarding-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-onboarding-ui --configuration=development", "build:package:plugin:onboarding-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-onboarding-ui --configuration=production", "build:package:plugin:onboarding-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-onboarding-ui --configuration=production", "build:package:plugin:public-layout-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-public-layout-ui --configuration=development", "build:package:plugin:public-layout-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-public-layout-ui --configuration=production", "build:package:plugin:public-layout-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-public-layout-ui --configuration=production", "build:package:plugin:job-proposal": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-proposal", "build:package:plugin:job-proposal:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-job-proposal", "build:package:plugin:job-proposal:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-job-proposal", "build:package:plugin:knowledge-base": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-knowledge-base", "build:package:plugin:knowledge-base:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-knowledge-base", "build:package:plugin:knowledge-base:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-knowledge-base", "build:package:plugin:videos-ui": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-videos-ui --configuration=development", "build:package:plugin:videos-ui:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-videos-ui --configuration=production", "build:package:plugin:videos-ui:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-videos-ui --configuration=production", "build:package:plugin:videos": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-videos", "build:package:plugin:videos:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-videos", "build:package:plugin:videos:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-videos", "build:package:plugin:registry": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-registry", "build:package:plugin:registry:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-registry", "build:package:plugin:registry:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-registry", "build:package:plugin:changelog": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-changelog", "build:package:plugin:changelog:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-changelog", "build:package:plugin:changelog:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-changelog", "build:package:plugin:integration-wakatime": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-wakatime", "build:package:plugin:integration-wakatime:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-integration-wakatime", "build:package:plugin:integration-wakatime:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-integration-wakatime", "build:package:plugin:camshot": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-camshot", "build:package:plugin:camshot:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-camshot", "build:package:plugin:camshot:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-camshot", "build:package:plugin:soundshot": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-soundshot", "build:package:plugin:soundshot:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build plugin-soundshot", "build:package:plugin:soundshot:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build plugin-soundshot", "build:package:all": "yarn run build:package:constants && yarn run build:package:contracts && yarn run build:package:common && yarn run build:package:utils && yarn run build:package:config && yarn run build:package:plugin && yarn run build:package:auth && yarn run build:package:plugins:pre && yarn run build:package:core && yarn run build:package:plugins:post && yarn run build:package:desktop-activity && yarn run build:package:desktop-lib && yarn run build:package:plugin:integration-wakatime && yarn run build:package:desktop-ui-lib && yarn run build:package:plugin:camshot && yarn run build:package:plugin:soundshot && yarn run build:mcp-server", "build:package:all:prod": "yarn run build:package:constants:prod && yarn run build:package:contracts:prod && yarn run build:package:common:prod && yarn run build:package:utils:prod && yarn run build:package:config:prod && yarn run build:package:plugin:prod && yarn run build:package:auth:prod && yarn run build:package:plugins:pre:prod && yarn run build:package:core:prod && yarn run build:package:plugins:post:prod && yarn run build:package:desktop-activity:prod && yarn run build:package:desktop-lib:prod && yarn run build:package:plugin:integration-wakatime:prod && yarn run build:package:desktop-ui-lib:prod && yarn run build:package:plugin:camshot:prod && yarn run build:package:plugin:soundshot:prod && yarn run build:mcp-server:prod", "build:package:all:docker": "yarn run build:package:constants:docker && yarn run build:package:contracts:docker && yarn run build:package:common:docker && yarn run build:package:utils:docker && yarn run build:package:config:docker && yarn run build:package:plugin:docker && yarn run build:package:auth:docker && yarn run build:package:plugins:pre:docker && yarn run build:package:core:docker && yarn run build:package:plugins:post:docker && yarn build:package:desktop-activity:docker && yarn run build:package:desktop-lib:docker && yarn run build:package:plugin:integration-wakatime:docker && yarn run build:package:desktop-ui-lib:docker && yarn run build:package:plugin:camshot:docker && yarn run build:package:plugin:soundshot:docker && yarn run build:mcp-server:docker", "build:package:api": "yarn run build:package:constants && yarn run build:package:contracts && yarn run build:package:common && yarn run build:package:utils && yarn run build:package:config && yarn run build:package:plugin && yarn run build:package:auth && yarn run build:package:core && yarn run build:package:plugins:post && yarn run build:package:plugin:integration-wakatime", "build:package:api:prod": "yarn run build:package:constants:prod && yarn run build:package:contracts:prod && yarn run build:package:common:prod && yarn run build:package:utils:prod && yarn run build:package:config:prod && yarn run build:package:plugin:prod && yarn run build:package:auth:prod && yarn run build:package:core:prod && yarn run build:package:plugins:post:prod", "build:package:api:docker": "yarn run build:package:constants:docker && yarn run build:package:contracts:docker && yarn run build:package:common:docker && yarn run build:package:utils:docker && yarn run build:package:config:docker && yarn run build:package:plugin:docker && yarn run build:package:auth:docker && yarn run build:package:core:docker && yarn run build:package:plugins:post:docker", "build:package:gauzy": "yarn run build:package:constants && yarn run build:package:contracts && yarn run build:package:common && yarn run build:package:utils && yarn run build:package:config && yarn run build:package:plugin && yarn run build:package:auth && yarn run build:package:plugins:pre && yarn run build:package:core && yarn run build:package:plugins:post", "build:package:gauzy:prod": "yarn run build:package:constants:prod && yarn run build:package:contracts:prod && yarn run build:package:common:prod && yarn run build:package:utils:prod && yarn run build:package:config:prod && yarn run build:package:plugin:prod && yarn run build:package:auth:prod && yarn run build:package:plugins:pre:prod && yarn run build:package:core:prod && yarn run build:package:plugins:post:prod", "build:package:gauzy:docker": "yarn run build:package:constants:docker && yarn run build:package:contracts:docker && yarn run build:package:common:docker && yarn run build:package:utils:docker && yarn run build:package:config:docker && yarn run build:package:plugin:docker && yarn run build:package:auth:docker && yarn run build:package:plugins:pre:docker && yarn run build:package:core:docker && yarn run build:package:plugins:post:docker", "build:package:desktop-activity": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-activity", "build:package:desktop-activity:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn nx build desktop-activity", "build:package:desktop-activity:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build desktop-activity", "copy-files-desktop": "copyfiles -f packages/core/src/**/*.gql dist/apps/desktop/data/", "prepare:desktop-timer": "yarn run postinstall.electron && tsc -p apps/desktop-timer/tsconfig.electron.json", "build:desktop-timer": "cross-env NODE_ENV=production yarn copy-files-i18n-desktop-timer && yarn run postinstall.electron && yarn run config:prod && yarn run config:desktop-timer:prod && yarn build:package:all:prod && yarn run pack:desktop-timer && yarn run generate:icons:desktop-timer --environment=prod && yarn ng:prod build desktop-timer --base-href=./ && yarn run prepare:desktop-timer && yarn ng:prod build desktop-api --configuration=production --output-path=dist/apps/desktop-timer/desktop-api && yarn run copy-assets-gauzy-timer", "build:desktop-timer:linux": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --project dist/apps/desktop-timer", "build:desktop-timer:linux:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/desktop-timer", "build:desktop-timer:linux:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/desktop-timer", "build:desktop-timer:windows": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --project dist/apps/desktop-timer", "build:desktop-timer:windows:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/desktop-timer", "build:desktop-timer:windows:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/desktop-timer", "build:desktop-timer:mac": "yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --project dist/apps/desktop-timer", "build:desktop-timer:mac:release": "yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish=always --project dist/apps/desktop-timer", "build:desktop-timer:mac:quick": "yarn run build:desktop-timer && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --config -c.mac.identity=null --project dist/apps/desktop-timer", "copy-assets-gauzy": "copyfiles -f apps/desktop/src/assets/snapshot-sound.wav dist/apps/desktop/data/sound/", "copy-assets-gauzy-timer": "copyfiles -f apps/desktop/src/assets/snapshot-sound.wav dist/apps/desktop-timer/data/sound/", "prepare:gauzy-server": "yarn run postinstall.electron && tsc -p apps/server/tsconfig.electron.json", "prepare:gauzy-server:dev": "yarn run postinstall.electron && tsc -p apps/server/tsconfig.dev.json", "build:gauzy-server": "cross-env NODE_ENV=production yarn run copy-files-i18n-server && yarn run postinstall.electron && yarn run config:prod && yarn run config:server:prod && yarn run build:package:all:prod && yarn run pack:server && yarn run generate:icons:server --environment=prod && yarn ng:prod build gauzy-server --base-href ./ && yarn run prepare:gauzy-server && yarn ng run gauzy:server-ui && yarn ng run api:gauzy-server-api && yarn run copy-files-gauzy-server", "build:gauzy-server:windows": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --project dist/apps/gauzy-server", "build:gauzy-server:windows:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/gauzy-server", "build:gauzy-server:windows:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/gauzy-server", "build:gauzy-server:linux": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --project dist/apps/gauzy-server", "build:gauzy-server:linux:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/gauzy-server", "build:gauzy-server:linux:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/gauzy-server", "build:gauzy-server:mac": "yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --project dist/apps/gauzy-server", "build:gauzy-server:mac:release": "yarn run build:gauzy-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish=always --project dist/apps/gauzy-server", "quick:build:gauzy-server": "yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --mac --project dist/apps/gauzy-server", "copy-files-i18n-desktop-timer": "yarn run download:translations --desktop=desktop-timer", "copy-files-i18n-desktop": "yarn run download:translations --desktop=desktop", "copy-files-i18n-server": "yarn run download:translations --desktop=server", "copy-files-gauzy-server": "copyfiles -f packages/core/src/**/*.gql dist/apps/gauzy-server/data/", "generate:icons": "yarn ts-node .scripts/icon-utils/icon-factory.ts", "generate:icons:desktop-timer": "yarn run generate:icons --desktop=desktop-timer", "generate:icons:desktop": "yarn run generate:icons --desktop=desktop", "generate:icons:server": "yarn run generate:icons --desktop=server", "pack": "yarn ts-node .scripts/electron-package-utils/package-util.ts", "pack:desktop-timer": "yarn run pack --desktop=desktop-timer", "pack:desktop": "yarn run pack --desktop=desktop", "pack:server": "yarn run pack --desktop=server", "pack:api-server": "yarn run pack --desktop=gauzy-api-server", "download:translations": "yarn ts-node .scripts/translation/translation-util.ts", "prepare:gauzy-api-server": "yarn run postinstall.electron && tsc -p apps/server-api/tsconfig.electron.json", "build:gauzy-api-server": "cross-env NODE_ENV=production yarn run copy-files-i18n-api-server && yarn run postinstall.electron && yarn run config:prod && yarn run config:server-api:prod && yarn build:package:all:prod && yarn run pack:api-server && yarn run generate:icons:server-api --environment=prod && yarn ng:prod build gauzy-api-server --base-href ./ && yarn run prepare:gauzy-api-server && yarn ng:prod run api:server-api && yarn run copy-files-gauzy-api-server", "build:gauzy-api-server:windows": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --project dist/apps/gauzy-api-server", "build:gauzy-api-server:windows:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/gauzy-api-server", "build:gauzy-api-server:windows:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/gauzy-api-server", "build:gauzy-api-server:linux": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --project dist/apps/gauzy-api-server", "build:gauzy-api-server:linux:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/gauzy-api-server", "build:gauzy-api-server:linux:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/gauzy-api-server", "build:gauzy-api-server:mac": "yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --project dist/apps/gauzy-api-server", "build:gauzy-api-server:mac:release": "yarn run build:gauzy-api-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish=always --project dist/apps/gauzy-api-server", "copy-files-i18n-api-server": "yarn run download:translations --desktop=server-api", "copy-files-gauzy-api-server": "copyfiles -f packages/core/src/**/*.gql dist/apps/gauzy-api-server/data/", "quick:build:gauzy-api-server": "yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --mac --project dist/apps/gauzy-api-server", "generate:icons:server-api": "yarn run generate:icons --desktop=server-api", "pack:agent": "yarn run pack --desktop=agent", "prepare:agent": "yarn run postinstall.electron && tsc -p apps/agent/tsconfig.electron.json", "prepare:agent:watch": "yarn run postinstall.electron && tsc --watch -p apps/agent/tsconfig.electron.json", "serve:agent:main": "cross-env NODE_ENV=development yarn config:agent:prod && yarn copy-files-i18n-agent && yarn prepare:agent && NODE_ENV=development NODE_PATH=dist/packages yarn electronmon ./dist/apps/agent/index.js", "serve:agent:ui": "yarn config:prod && yarn config:agent:prod && yarn ng serve agent --configuration production", "serve:agent": "concurrently \"yarn serve:agent:ui\" \"wait-on http://localhost:4200 && yarn serve:agent:main\"", "build:agent": "cross-env NODE_ENV=production yarn copy-files-i18n-agent && yarn run postinstall.electron && yarn run config:agent:prod && yarn run generate:icons:agent --environment=prod && yarn config:prod && yarn build:package:all:prod && yarn pack:agent && yarn ng:prod build agent --base-href=./ && yarn run prepare:agent && yarn copy-assets-agent", "copy-files-i18n-agent": "yarn run download:translations --desktop=agent", "config:agent:prod": "yarn run config:electron -- --environment=prod --desktop=agent", "dev:agent": "electronmon --exec ts-node ./apps/agent/src/index.ts", "generate:icons:agent": "yarn run generate:icons --desktop=agent", "build:agent:windows": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --project dist/apps/agent", "build:agent:windows:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/agent", "build:agent:windows:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/agent", "build:agent:linux": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --project dist/apps/agent", "build:agent:linux:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/agent", "build:agent:linux:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/agent", "build:agent:mac": "yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --project dist/apps/agent", "build:agent:mac:release": "yarn run build:agent && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish=always --project dist/apps/agent", "copy-assets-agent": "copyfiles -f apps/agent/src/assets/snapshot-sound.wav dist/apps/agent/data/sound/", "build:mcp-server": "yarn nx build mcp-server", "build:mcp-server:prod": "yarn nx build mcp-server --configuration=production", "build:mcp-server:docker": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn nx build mcp-server", "test:mcp-server": "yarn nx test mcp-server", "build:mcp": "yarn nx build mcp --configuration=development", "build:mcp:prod": "yarn nx build mcp --configuration=production", "start:mcp": "yarn nx serve mcp", "test:mcp": "yarn nx test mcp", "build:server-mcp": "yarn nx build server-mcp --configuration=development", "build:server-mcp:prod": "yarn nx build server-mcp --configuration=production", "build:server-mcp:electron": "yarn nx build server-mcp --configuration=development", "build:server-mcp:electron:prod": "yarn nx build server-mcp --configuration=production", "start:server-mcp": "yarn nx serve server-mcp", "start:server-mcp:electron": "yarn build:server-mcp:electron && node apps/server-mcp/electron-start.js", "serve:server-mcp": "yarn nx serve server-mcp", "serve:server-mcp:prod": "yarn nx serve server-mcp --configuration=production", "test:server-mcp": "yarn nx test server-mcp", "config:server-mcp:prod": "yarn run config:electron -- --environment=prod --desktop=server-mcp", "prepare:server-mcp": "yarn run postinstall.electron && tsc -p apps/server-mcp/tsconfig.electron.json", "pack:server-mcp": "yarn run pack --desktop=server-mcp", "build:gauzy-mcp-server": "cross-env NODE_ENV=production yarn run copy-files-i18n-mcp-server && yarn run postinstall.electron && yarn run config:prod && yarn run config:server-mcp:prod && yarn run build:package:all:prod && yarn run pack:server-mcp && yarn run generate:icons:server-mcp --environment=prod && yarn nx build server-mcp --configuration=production --base-href ./ && yarn run prepare:server-mcp && yarn run copy-files-gauzy-mcp-server", "build:gauzy-mcp-server:windows": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --project dist/apps/server-mcp", "build:gauzy-mcp-server:windows:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/server-mcp", "build:gauzy-mcp-server:windows:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --windows --publish=always --project dist/apps/server-mcp", "build:gauzy-mcp-server:linux": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --project dist/apps/server-mcp", "build:gauzy-mcp-server:linux:release": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=12288 yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/server-mcp", "build:gauzy-mcp-server:linux:release:gh": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=60000 yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --linux --publish=always --project dist/apps/server-mcp", "build:gauzy-mcp-server:mac": "yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --project dist/apps/server-mcp", "build:gauzy-mcp-server:mac:release": "yarn run build:gauzy-mcp-server && npm config set cache .cache && yarn electron-builder -c.electronVersion=30.0.1 build --mac --publish=always --project dist/apps/server-mcp", "copy-files-i18n-mcp-server": "yarn run download:translations --desktop=server-mcp", "copy-files-gauzy-mcp-server": "copyfiles -f packages/core/src/**/*.gql dist/apps/server-mcp/data/", "quick:build:gauzy-mcp-server": "yarn electron-builder -c.electronVersion=30.0.1 -c.extraMetadata.author.name=Ever build --mac --project dist/apps/server-mcp", "generate:icons:server-mcp": "yarn run generate:icons --desktop=server-mcp", "build:mcp:all": "yarn build:mcp-server && yarn build:mcp && yarn build:server-mcp", "build:mcp:all:prod": "yarn build:mcp-server:prod && yarn build:mcp:prod && yarn build:server-mcp:prod", "test:mcp:all": "yarn test:mcp-server && yarn test:mcp && yarn test:server-mcp"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "pretty-quick --no-verify --staged"}}, "lint-staged": {}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "publish": ["@semantic-release/github"], "generateNotes": {"preset": "angular"}, "npmPublish": false}, "workspaces": {"packages": ["apps/*", "tools", "packages/*", "packages/plugins/*"], "nohoist": ["**/@types/jasmine", "**/@types/jasminewd2", "**/@angular*/**", "**/@ngtools/**"]}, "resolutions": {"@angular-devkit/architect": "^0.1902.11", "@angular-devkit/core": "^19.2.11", "@typescript-eslint/scope-manager": "^8.31.0", "@typescript-eslint/utils": "^8.31.0", "@typescript-eslint/visitor-keys": "^8.31.0", "camelcase": "^6.3.0", "rxjs": "^7.8.2", "autoprefixer": "^10.4.20", "locutus": "^2.0.30", "minizlib": "^2.1.2"}, "dependencies": {"@angular/animations": "^19.2.10", "@angular/cdk": "^19.2.11", "@angular/common": "^19.2.10", "@angular/compiler": "^19.2.10", "@angular/core": "^19.2.10", "@angular/forms": "^19.2.10", "@angular/language-service": "^19.2.10", "@angular/material": "^19.2.11", "@angular/platform-browser": "^19.2.10", "@angular/platform-browser-dynamic": "^19.2.10", "@angular/router": "^19.2.10", "@angular/service-worker": "^19.2.10", "@fortawesome/angular-fontawesome": "^1.0.0", "@fullcalendar/angular": "^6.1.17", "@nebular/auth": "^15.0.0", "@nebular/bootstrap": "^9.1.0-rc.6", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.1.0", "@nestjs/terminus": "^11.0.0", "@ng-select/ng-select": "^14.8.1", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "angular2-smart-table": "^3.6.2", "autoprefixer": "^10.4.20", "bcrypt": "^5.1.1", "camelcase": "^6.3.0", "dotenv": "^16.0.3", "electron": "^30.0.1", "jsdom": "^23.0.1", "lodash-es": "^4.17.21", "minizlib": "^2.1.2", "mobx": "~4.14.1", "ng2-file-upload": "^8.0.0", "ngx-draggable-dom": "^19.0.7", "ngx-infinite-scroll": "^19.0.0", "ngx-permissions": "^19.0.0", "parse5": "^7.1.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "tslib": "^2.6.2", "yargs": "^17.5.0", "zone.js": "0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.1", "@angular-devkit/architect": "^0.1902.11", "@angular-devkit/build-angular": "^19.2.11", "@angular-devkit/build-ng-packagr": "^0.1002.0", "@angular-devkit/core": "^19.2.11", "@angular-devkit/schematics": "^19.2.11", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^19.2.11", "@angular/compiler-cli": "^19.2.10", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@commitlint/config-lerna-scopes": "^19.5.0", "@commitlint/travis-cli": "^19.6.1", "@compodoc/compodoc": "^1.1.19", "@cypress/browserify-preprocessor": "^3.0.2", "@electron/notarize": "^2.5.0", "@electron/remote": "^2.0.8", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.0", "@nstudio/angular": "^20.0.3", "@nstudio/electron": "^20.0.3", "@nstudio/electron-angular": "^20.0.3", "@nstudio/web": "^20.0.3", "@nstudio/web-angular": "^20.0.3", "@nstudio/xplat": "^20.0.3", "@nx/angular": "20.8.0", "@nx/cypress": "20.8.0", "@nx/devkit": "20.8.0", "@nx/eslint": "20.8.0", "@nx/eslint-plugin": "20.8.0", "@nx/eslint-plugin-nx": "^16.0.0-beta.1", "@nx/jest": "20.8.0", "@nx/js": "20.8.0", "@nx/nest": "20.8.0", "@nx/node": "20.8.0", "@nx/web": "20.8.0", "@nx/webpack": "20.8.0", "@nx/workspace": "20.8.0", "@schematics/angular": "^19.2.11", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.2.6", "@semantic-release/npm": "^11.0.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/bcrypt": "^5.0.2", "@types/google.maps": "^3.58.1", "@types/jest": "29.5.14", "@types/jsdom": "^21.1.6", "@types/node": "^20.14.9", "@types/webpack": "^5.28.5", "@types/yargs": "^15.0.9", "@typescript-eslint/utils": "^8.0.0", "ajv-formats": "^2.1.1", "angular-eslint": "^19.3.0", "cloc": "^2.7.0", "codecov": "^3.8.3", "commitizen": "^4.2.1", "concurrently": "^5.3.0", "conventional-changelog": "^3.1.23", "conventional-changelog-cli": "^2.1.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "cspell": "^6.18.1", "cypress": "^9.4.1", "cypress-cucumber-preprocessor": "^4.3.1", "cypress-file-upload": "^5.0.8", "cz-conventional-changelog": "^3.3.0", "electron-builder": "^24.13.3", "envalid": "^6.0.2", "esbuild": "0.24.0", "eslint-plugin-cypress": "2.13.4", "fork-ts-checker-webpack-plugin": "^5.2.0", "gulp-tag-version": "^1.3.1", "husky": "^6.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.7.0", "jest-jasmine2": "29.7.0", "jest-preset-angular": "14.5.5", "jsonc-eslint-parser": "^2.1.0", "lerna": "^8.1.9", "lerna-changelog": "^2.2.0", "lighthouse": "^6.3.0", "lint-staged": "^10.4.0", "ng-packagr": "19.2.2", "node-gyp": "^10.2.0", "npm-run-all": "^4.1.5", "nx": "20.8.0", "pkg": "^5.3.0", "png-to-ico": "^2.1.8", "postcss": "^8.4.5", "postcss-import": "~14.1.0", "postcss-preset-env": "~7.5.0", "postcss-url": "~10.1.3", "prettier": "^2.8.4", "prettier-tslint": "^0.4.2", "pretty-quick": "^3.1.0", "protractor": "^7.0.0", "rimraf": "^3.0.2", "semantic-release": "^22.0.12", "simple-git": "^3.20.0", "stylelint": "^15.10.1", "ts-jest": "29.1.1", "ts-loader": "^8.0.4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "tslint-language-service": "^0.9.9", "typedoc": "^0.23.24", "typescript": "^5.8.3", "verdaccio": "^6.0.5", "webpack": "^5.99.7", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}, "twing": {"locutus": "2.0.30"}}, "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "prettier": {"printWidth": 120, "singleQuote": true, "semi": true, "useTabs": true, "tabWidth": 4, "arrowParens": "always", "trailingComma": "none", "quoteProps": "as-needed", "trimTrailingWhitespace": true, "overrides": [{"files": "*.scss", "options": {"useTabs": false, "tabWidth": 2}}, {"files": "*.yml", "options": {"useTabs": false, "tabWidth": 2}}]}, "snyk": true, "xplat": {"prefix": "gauzy", "framework": "angular"}, "nx": {"includedScripts": []}}